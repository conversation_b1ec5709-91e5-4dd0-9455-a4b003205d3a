<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Traits\DatabaseReconnection;
use PDOException;

class DatabaseHealthCheck extends Command
{
    use DatabaseReconnection;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:health-check 
                            {--fix : Attempt to fix connection issues}
                            {--verbose : Show detailed output}
                            {--log : Log results to file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check database connection health and diagnose issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 Starting Database Health Check...');
        $this->newLine();

        $results = [];
        $overallHealth = true;

        // Test 1: Basic Connection
        $results['connection'] = $this->testConnection();
        if (!$results['connection']['status']) {
            $overallHealth = false;
        }

        // Test 2: Query Execution
        $results['query'] = $this->testQueryExecution();
        if (!$results['query']['status']) {
            $overallHealth = false;
        }

        // Test 3: Transaction Support
        $results['transaction'] = $this->testTransactions();
        if (!$results['transaction']['status']) {
            $overallHealth = false;
        }

        // Test 4: Server Variables
        $results['variables'] = $this->checkServerVariables();

        // Test 5: Connection Pool Status
        $results['pool'] = $this->checkConnectionPool();

        // Display results
        $this->displayResults($results, $overallHealth);

        // Attempt fixes if requested
        if ($this->option('fix') && !$overallHealth) {
            $this->attemptFixes($results);
        }

        // Log results if requested
        if ($this->option('log')) {
            $this->logResults($results, $overallHealth);
        }

        return $overallHealth ? 0 : 1;
    }

    /**
     * Test basic database connection
     */
    private function testConnection(): array
    {
        $this->info('📡 Testing database connection...');
        
        try {
            $pdo = DB::connection()->getPdo();
            $this->info('✅ Database connection successful');
            
            return [
                'status' => true,
                'message' => 'Connection successful',
                'details' => [
                    'driver' => $pdo->getAttribute(\PDO::ATTR_DRIVER_NAME),
                    'version' => $pdo->getAttribute(\PDO::ATTR_SERVER_VERSION),
                ]
            ];
        } catch (\Exception $e) {
            $this->error('❌ Database connection failed: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * Test query execution
     */
    private function testQueryExecution(): array
    {
        $this->info('🔍 Testing query execution...');
        
        try {
            $result = DB::select('SELECT 1 as test, NOW() as current_time');
            $this->info('✅ Query execution successful');
            
            return [
                'status' => true,
                'message' => 'Query execution successful',
                'details' => $result[0] ?? null
            ];
        } catch (\Exception $e) {
            $this->error('❌ Query execution failed: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * Test transaction support
     */
    private function testTransactions(): array
    {
        $this->info('🔄 Testing transaction support...');
        
        try {
            DB::transaction(function () {
                DB::select('SELECT 1');
                // This will be rolled back automatically
            });
            
            $this->info('✅ Transaction support working');
            
            return [
                'status' => true,
                'message' => 'Transaction support working'
            ];
        } catch (\Exception $e) {
            $this->error('❌ Transaction test failed: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * Check important server variables
     */
    private function checkServerVariables(): array
    {
        $this->info('⚙️ Checking server variables...');
        
        try {
            $variables = [
                'wait_timeout',
                'interactive_timeout',
                'max_allowed_packet',
                'max_connections',
                'thread_cache_size',
                'query_cache_size',
                'innodb_buffer_pool_size'
            ];
            
            $results = [];
            foreach ($variables as $variable) {
                $result = DB::select("SHOW VARIABLES LIKE ?", [$variable]);
                if (!empty($result)) {
                    $results[$variable] = $result[0]->Value;
                }
            }
            
            $this->info('✅ Server variables retrieved');
            
            return [
                'status' => true,
                'message' => 'Server variables retrieved',
                'details' => $results
            ];
        } catch (\Exception $e) {
            $this->error('❌ Failed to retrieve server variables: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Check connection pool status
     */
    private function checkConnectionPool(): array
    {
        $this->info('🏊 Checking connection pool...');
        
        try {
            $status = DB::select("SHOW STATUS LIKE 'Threads_%'");
            $processlist = DB::select("SHOW PROCESSLIST");
            
            $this->info('✅ Connection pool status retrieved');
            
            return [
                'status' => true,
                'message' => 'Connection pool healthy',
                'details' => [
                    'thread_status' => $status,
                    'active_connections' => count($processlist)
                ]
            ];
        } catch (\Exception $e) {
            $this->error('❌ Failed to check connection pool: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Display test results
     */
    private function displayResults(array $results, bool $overallHealth): void
    {
        $this->newLine();
        $this->info('📊 Health Check Results:');
        $this->newLine();

        foreach ($results as $test => $result) {
            $status = $result['status'] ? '✅' : '❌';
            $this->line("$status " . ucfirst($test) . ": " . $result['message']);
            
            if ($this->option('verbose') && isset($result['details'])) {
                $this->line('   Details: ' . json_encode($result['details'], JSON_PRETTY_PRINT));
            }
        }

        $this->newLine();
        $overallStatus = $overallHealth ? '✅ HEALTHY' : '❌ UNHEALTHY';
        $this->line("Overall Status: $overallStatus");
    }

    /**
     * Attempt to fix connection issues
     */
    private function attemptFixes(array $results): void
    {
        $this->newLine();
        $this->info('🔧 Attempting to fix issues...');

        if (!$results['connection']['status']) {
            $this->info('Attempting database reconnection...');
            try {
                $this->reconnectDatabase();
                $this->info('✅ Database reconnection successful');
            } catch (\Exception $e) {
                $this->error('❌ Reconnection failed: ' . $e->getMessage());
            }
        }
    }

    /**
     * Log results to file
     */
    private function logResults(array $results, bool $overallHealth): void
    {
        $logData = [
            'timestamp' => now()->toISOString(),
            'overall_health' => $overallHealth,
            'results' => $results
        ];

        Log::channel('single')->info('Database Health Check Results', $logData);
        $this->info('📝 Results logged to storage/logs/laravel.log');
    }
}
