<?php
/**
 * MySQL Diagnostics Runner
 * 
 * This script runs all diagnostic tests in sequence and provides
 * a comprehensive report on MySQL server health and configuration.
 */

echo "🔍 MySQL Server Comprehensive Diagnostics\n";
echo "=========================================\n\n";

echo "This script will run a complete diagnostic suite to identify\n";
echo "MySQL server issues that may cause 'server has gone away' errors.\n\n";

echo "The following tests will be performed:\n";
echo "1. 📊 Server Configuration Analysis\n";
echo "2. ⚙️  Server Variables Validation\n";
echo "3. 🔗 Direct Connection Testing\n";
echo "4. ⏱️  Server Stability Monitoring\n\n";

// Ask user for confirmation
echo "Do you want to proceed? (y/n): ";
$handle = fopen("php://stdin", "r");
$input = trim(fgets($handle));
fclose($handle);

if (strtolower($input) !== 'y' && strtolower($input) !== 'yes') {
    echo "Diagnostics cancelled.\n";
    exit(0);
}

echo "\n🚀 Starting comprehensive diagnostics...\n\n";

// Function to run a script and capture output
function runDiagnosticScript($script_name, $description) {
    echo "=" . str_repeat("=", strlen($description) + 2) . "=\n";
    echo " {$description} \n";
    echo "=" . str_repeat("=", strlen($description) + 2) . "=\n\n";
    
    if (!file_exists($script_name)) {
        echo "❌ Script not found: {$script_name}\n\n";
        return false;
    }
    
    $start_time = microtime(true);
    
    // Execute the script
    ob_start();
    include $script_name;
    $output = ob_get_clean();
    
    $execution_time = microtime(true) - $start_time;
    
    echo $output;
    echo "\n⏱️  Execution time: " . number_format($execution_time, 2) . " seconds\n\n";
    
    return true;
}

// Function to create a summary report
function createSummaryReport($results) {
    echo "📋 COMPREHENSIVE DIAGNOSTIC SUMMARY\n";
    echo "===================================\n\n";
    
    $total_tests = count($results);
    $passed_tests = count(array_filter($results));
    $failed_tests = $total_tests - $passed_tests;
    
    echo "Total Tests Run: {$total_tests}\n";
    echo "Tests Passed: {$passed_tests}\n";
    echo "Tests Failed: {$failed_tests}\n";
    echo "Success Rate: " . number_format(($passed_tests / $total_tests) * 100, 1) . "%\n\n";
    
    echo "Test Results:\n";
    foreach ($results as $test => $passed) {
        $status = $passed ? "✅ PASSED" : "❌ FAILED";
        echo "- {$test}: {$status}\n";
    }
    
    echo "\n";
    
    if ($failed_tests > 0) {
        echo "🔧 RECOMMENDED ACTIONS:\n";
        echo "======================\n";
        echo "1. Review the failed test outputs above\n";
        echo "2. Check MySQL error logs in XAMPP\\mysql\\data\\\n";
        echo "3. Apply the optimized configuration (xampp_mysql_optimized.ini)\n";
        echo "4. Restart MySQL service in XAMPP Control Panel\n";
        echo "5. Re-run this diagnostic suite\n\n";
        
        echo "📁 Key Files to Check:\n";
        echo "- Configuration: XAMPP\\mysql\\bin\\my.ini\n";
        echo "- Error Log: XAMPP\\mysql\\data\\mysql_error.log\n";
        echo "- Data Directory: XAMPP\\mysql\\data\\\n\n";
        
        echo "🚨 If MySQL won't start after configuration changes:\n";
        echo "1. Restore backup: copy my.ini.backup my.ini\n";
        echo "2. Check error logs for specific issues\n";
        echo "3. Verify syntax in configuration file\n\n";
    } else {
        echo "🎉 ALL TESTS PASSED!\n";
        echo "===================\n";
        echo "Your MySQL server appears to be properly configured.\n";
        echo "If you're still experiencing 'server has gone away' errors,\n";
        echo "the issue may be application-specific or network-related.\n\n";
        
        echo "📝 Next Steps:\n";
        echo "1. Test your Laravel application\n";
        echo "2. Monitor error logs during normal usage\n";
        echo "3. Consider implementing the Laravel reconnection features\n\n";
    }
    
    echo "📖 For detailed troubleshooting, see: XAMPP_MYSQL_TROUBLESHOOTING_GUIDE.md\n";
}

// Store test results
$test_results = [];

// Test 1: Server Configuration Analysis
$test_results['Server Configuration Analysis'] = runDiagnosticScript(
    'xampp_mysql_diagnostics.php',
    '📊 Test 1: Server Configuration Analysis'
);

echo "\nPress Enter to continue to the next test...";
fgets(fopen("php://stdin", "r"));

// Test 2: Server Variables Validation
$test_results['Server Variables Validation'] = runDiagnosticScript(
    'mysql_variables_validator.php',
    '⚙️  Test 2: Server Variables Validation'
);

echo "\nPress Enter to continue to the next test...";
fgets(fopen("php://stdin", "r"));

// Test 3: Direct Connection Testing
$test_results['Direct Connection Testing'] = runDiagnosticScript(
    'direct_connection_test.php',
    '🔗 Test 3: Direct Connection Testing'
);

echo "\nPress Enter to continue to the final test...";
fgets(fopen("php://stdin", "r"));

// Test 4: Server Stability Monitoring (shorter version for diagnostics)
echo "=" . str_repeat("=", 40) . "=\n";
echo " ⏱️  Test 4: Server Stability Monitoring \n";
echo "=" . str_repeat("=", 40) . "=\n\n";

echo "Note: Running a shortened stability test (60 seconds instead of 5 minutes)\n";
echo "For a full stability test, run: php mysql_stability_test.php\n\n";

// Modify the stability test for shorter duration
$stability_script = file_get_contents('mysql_stability_test.php');
$modified_script = str_replace('$test_duration = 300;', '$test_duration = 60;', $stability_script);
$modified_script = str_replace('$check_interval = 10;', '$check_interval = 5;', $modified_script);

// Write temporary script
file_put_contents('temp_stability_test.php', $modified_script);

$test_results['Server Stability Monitoring'] = runDiagnosticScript(
    'temp_stability_test.php',
    'Short Stability Test (60 seconds)'
);

// Clean up temporary file
if (file_exists('temp_stability_test.php')) {
    unlink('temp_stability_test.php');
}

// Generate summary report
createSummaryReport($test_results);

// Save results to file
$timestamp = date('Y-m-d_H-i-s');
$report_filename = "mysql_diagnostic_report_{$timestamp}.txt";

ob_start();
createSummaryReport($test_results);
$summary_content = ob_get_clean();

file_put_contents($report_filename, $summary_content);

echo "📄 Diagnostic report saved to: {$report_filename}\n\n";

echo "✅ Comprehensive diagnostics completed!\n";
echo "Thank you for using the MySQL diagnostic suite.\n";
?>
