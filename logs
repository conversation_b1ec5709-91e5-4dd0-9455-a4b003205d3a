2025-07-05 20:30:43 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-05 20:30:43 0 [Note] InnoDB: Uses event mutexes
2025-07-05 20:30:43 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-05 20:30:43 0 [Note] InnoDB: Number of pools: 1
2025-07-05 20:30:43 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-05 20:30:43 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-05 20:30:43 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-05 20:30:43 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=300288
2025-07-05 20:30:43 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-05 20:30:43 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-05 20:30:43 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-05 20:30:43 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-05 20:30:43 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-05 20:30:43 0 [Note] InnoDB: Waiting for purge to start
2025-07-05 20:30:43 0 [Note] InnoDB: 10.4.22 started; log sequence number 300297; transaction id 170
2025-07-05 20:30:43 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-05 20:30:43 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-05 20:30:43 0 [Note] InnoDB: Buffer pool(s) load completed at 250705 20:30:43
2025-07-05 20:30:43 0 [Note] Server socket created on IP: '::'.
2025-07-05 20:30:43 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-07-05 20:30:43 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-07-05 20:30:43 0 [ERROR] Aborting
2025-07-06 17:40:23 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-06 17:40:23 0 [Note] InnoDB: Uses event mutexes
2025-07-06 17:40:23 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-06 17:40:23 0 [Note] InnoDB: Number of pools: 1
2025-07-06 17:40:23 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-06 17:40:23 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-06 17:40:23 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-06 17:40:23 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-06 17:40:23 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-06 17:40:23 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-06 17:40:23 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-06 17:40:23 0 [Note] InnoDB: Waiting for purge to start
2025-07-06 17:40:24 0 [Note] InnoDB: 10.4.22 started; log sequence number 300306; transaction id 170
2025-07-06 17:40:24 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-06 17:40:24 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-06 17:40:24 0 [Note] InnoDB: Buffer pool(s) load completed at 250706 17:40:24
2025-07-06 17:40:24 0 [Note] Server socket created on IP: '::'.
2025-07-06 17:40:24 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-07-06 17:40:24 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-07-06 17:40:24 0 [ERROR] Aborting
2025-07-06 17:40:30 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-06 17:40:30 0 [Note] InnoDB: Uses event mutexes
2025-07-06 17:40:30 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-06 17:40:30 0 [Note] InnoDB: Number of pools: 1
2025-07-06 17:40:30 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-06 17:40:30 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-06 17:40:30 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-06 17:40:30 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-06 17:40:30 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-06 17:40:30 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-06 17:40:30 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-06 17:40:30 0 [Note] InnoDB: Waiting for purge to start
2025-07-06 17:40:30 0 [Note] InnoDB: 10.4.22 started; log sequence number 300315; transaction id 170
2025-07-06 17:40:30 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-06 17:40:30 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-06 17:40:30 0 [Note] Server socket created on IP: '::'.
2025-07-06 17:40:30 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-07-06 17:40:30 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-07-06 17:40:30 0 [ERROR] Aborting
2025-07-06 17:40:30 0 [Note] InnoDB: Buffer pool(s) load completed at 250706 17:40:30
2025-07-06 17:40:41 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-06 17:40:41 0 [Note] InnoDB: Uses event mutexes
2025-07-06 17:40:41 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-06 17:40:41 0 [Note] InnoDB: Number of pools: 1
2025-07-06 17:40:41 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-06 17:40:41 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-06 17:40:41 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-06 17:40:41 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-06 17:40:41 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-06 17:40:41 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-06 17:40:41 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-06 17:40:41 0 [Note] InnoDB: Waiting for purge to start
2025-07-06 17:40:41 0 [Note] InnoDB: 10.4.22 started; log sequence number 300324; transaction id 170
2025-07-06 17:40:41 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-06 17:40:41 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-06 17:40:41 0 [Note] Server socket created on IP: '::'.
2025-07-06 17:40:41 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-07-06 17:40:41 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-07-06 17:40:41 0 [ERROR] Aborting
2025-07-06 17:43:04 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-06 17:43:04 0 [Note] InnoDB: Uses event mutexes
2025-07-06 17:43:04 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-06 17:43:04 0 [Note] InnoDB: Number of pools: 1
2025-07-06 17:43:04 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-06 17:43:04 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-06 17:43:04 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-06 17:43:04 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-06 17:43:04 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-06 17:43:04 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-06 17:43:04 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-06 17:43:04 0 [Note] InnoDB: Waiting for purge to start
2025-07-06 17:43:04 0 [Note] InnoDB: 10.4.22 started; log sequence number 300333; transaction id 170
2025-07-06 17:43:04 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-06 17:43:04 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-06 17:43:04 0 [Note] Server socket created on IP: '::'.
2025-07-06 17:43:04 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-07-06 17:43:04 0 [Note] InnoDB: Buffer pool(s) load completed at 250706 17:43:04
2025-07-06 17:43:04 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-07-06 17:43:04 0 [ERROR] Aborting
2025-07-07 22:49:11 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-07 22:49:11 0 [Note] InnoDB: Uses event mutexes
2025-07-07 22:49:11 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-07 22:49:11 0 [Note] InnoDB: Number of pools: 1
2025-07-07 22:49:11 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-07 22:49:11 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-07 22:49:11 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-07 22:49:11 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-07 22:49:11 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-07 22:49:11 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-07 22:49:11 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-07 22:49:11 0 [Note] InnoDB: Waiting for purge to start
2025-07-07 22:49:12 0 [Note] InnoDB: 10.4.22 started; log sequence number 300342; transaction id 170
2025-07-07 22:49:12 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-07 22:49:12 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-07 22:49:12 0 [Note] InnoDB: Buffer pool(s) load completed at 250707 22:49:12
2025-07-07 22:49:12 0 [Note] Server socket created on IP: '::'.
2025-07-08  0:01:24 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-08  0:01:24 0 [Note] InnoDB: Uses event mutexes
2025-07-08  0:01:24 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-08  0:01:24 0 [Note] InnoDB: Number of pools: 1
2025-07-08  0:01:24 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-08  0:01:24 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-08  0:01:24 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-08  0:01:24 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2349811
2025-07-08  0:01:24 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-08  0:01:24 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-08  0:01:24 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-08  0:01:24 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-08  0:01:24 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-08  0:01:24 0 [Note] InnoDB: Waiting for purge to start
2025-07-08  0:01:24 0 [Note] InnoDB: 10.4.22 started; log sequence number 2349820; transaction id 2519
2025-07-08  0:01:24 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-08  0:01:24 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-08  0:01:24 0 [Note] Server socket created on IP: '::'.
2025-07-10 19:45:51 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-10 19:45:51 0 [Note] InnoDB: Uses event mutexes
2025-07-10 19:45:51 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-10 19:45:51 0 [Note] InnoDB: Number of pools: 1
2025-07-10 19:45:51 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-10 19:45:51 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-10 19:45:51 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-10 19:45:51 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2463764
2025-07-10 19:45:52 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-10 19:45:52 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-10 19:45:52 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-10 19:45:52 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-10 19:45:52 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-10 19:45:52 0 [Note] InnoDB: Waiting for purge to start
2025-07-10 19:45:52 0 [Note] InnoDB: 10.4.22 started; log sequence number 2463773; transaction id 3133
2025-07-10 19:45:52 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-10 19:45:52 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-10 19:45:52 0 [Note] InnoDB: Buffer pool(s) load completed at 250710 19:45:52
2025-07-10 19:45:52 0 [Note] Server socket created on IP: '::'.
2025-07-10 19:45:57 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-10 19:45:57 0 [Note] InnoDB: Uses event mutexes
2025-07-10 19:45:57 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-10 19:45:57 0 [Note] InnoDB: Number of pools: 1
2025-07-10 19:45:57 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-10 19:45:57 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-10 19:45:57 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-10 19:45:57 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2463764
2025-07-10 19:45:57 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-10 19:45:57 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-10 19:45:57 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-10 19:45:57 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-10 19:45:57 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-10 19:45:57 0 [Note] InnoDB: Waiting for purge to start
2025-07-10 19:45:57 0 [Note] InnoDB: 10.4.22 started; log sequence number 2463773; transaction id 3133
2025-07-10 19:45:57 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-10 19:45:57 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-10 19:45:57 0 [Note] Server socket created on IP: '::'.
2025-07-12 15:51:39 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-12 15:51:39 0 [Note] InnoDB: Uses event mutexes
2025-07-12 15:51:39 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-12 15:51:39 0 [Note] InnoDB: Number of pools: 1
2025-07-12 15:51:39 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-12 15:51:39 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-12 15:51:39 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-12 15:51:39 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2463782
2025-07-12 15:51:40 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-12 15:51:40 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-12 15:51:40 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-12 15:51:40 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-12 15:51:40 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-12 15:51:40 0 [Note] InnoDB: Waiting for purge to start
2025-07-12 15:51:40 0 [Note] InnoDB: 10.4.22 started; log sequence number 2463791; transaction id 3133
2025-07-12 15:51:40 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-12 15:51:40 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-12 15:51:40 0 [Note] InnoDB: Buffer pool(s) load completed at 250712 15:51:40
2025-07-12 15:51:40 0 [Note] Server socket created on IP: '::'.
2025-07-12 15:51:48 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-12 15:51:48 0 [Note] InnoDB: Uses event mutexes
2025-07-12 15:51:48 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-12 15:51:48 0 [Note] InnoDB: Number of pools: 1
2025-07-12 15:51:48 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-12 15:51:48 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-12 15:51:48 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-12 15:51:48 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2463782
2025-07-12 15:51:48 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-12 15:51:48 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-12 15:51:48 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-12 15:51:48 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-12 15:51:48 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-12 15:51:48 0 [Note] InnoDB: Waiting for purge to start
2025-07-12 15:51:48 0 [Note] InnoDB: 10.4.22 started; log sequence number 2463791; transaction id 3133
2025-07-12 15:51:48 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-12 15:51:48 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-12 15:51:48 0 [Note] Server socket created on IP: '::'.
2025-07-29 17:51:08 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-07-29 17:51:08 0 [Note] InnoDB: Uses event mutexes
2025-07-29 17:51:08 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-07-29 17:51:08 0 [Note] InnoDB: Number of pools: 1
2025-07-29 17:51:08 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-07-29 17:51:08 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-07-29 17:51:08 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-29 17:51:08 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2468108
2025-07-29 17:51:08 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-07-29 17:51:08 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-07-29 17:51:08 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-29 17:51:08 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-29 17:51:08 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-07-29 17:51:08 0 [Note] InnoDB: Waiting for purge to start
2025-07-29 17:51:09 0 [Note] InnoDB: 10.4.22 started; log sequence number 2468117; transaction id 3188
2025-07-29 17:51:09 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-07-29 17:51:09 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-07-29 17:51:09 0 [Note] InnoDB: Buffer pool(s) load completed at 250729 17:51:09
2025-07-29 17:51:09 0 [Note] Server socket created on IP: '::'.
2025-08-06 19:36:58 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-06 19:36:58 0 [Note] InnoDB: Uses event mutexes
2025-08-06 19:36:58 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-06 19:36:58 0 [Note] InnoDB: Number of pools: 1
2025-08-06 19:36:58 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-06 19:36:58 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-06 19:36:58 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-06 19:36:58 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2476667
2025-08-06 19:36:59 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-06 19:36:59 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-06 19:36:59 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-06 19:36:59 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-06 19:36:59 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-06 19:36:59 0 [Note] InnoDB: Waiting for purge to start
2025-08-06 19:36:59 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476676; transaction id 3244
2025-08-06 19:36:59 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-06 19:36:59 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-06 19:36:59 0 [Note] InnoDB: Buffer pool(s) load completed at 250806 19:36:59
2025-08-06 19:36:59 0 [Note] Server socket created on IP: '::'.
2025-08-06 19:53:06 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-06 19:53:06 0 [Note] InnoDB: Uses event mutexes
2025-08-06 19:53:06 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-06 19:53:06 0 [Note] InnoDB: Number of pools: 1
2025-08-06 19:53:06 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-06 19:53:07 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-06 19:53:07 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-06 19:53:07 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2476826
2025-08-06 19:53:07 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-06 19:53:07 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-06 19:53:07 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-06 19:53:07 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-06 19:53:07 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-06 19:53:07 0 [Note] InnoDB: Waiting for purge to start
2025-08-06 19:53:08 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476835; transaction id 3244
2025-08-06 19:53:08 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-06 19:53:08 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-06 19:53:08 0 [Note] InnoDB: Buffer pool(s) load completed at 250806 19:53:08
2025-08-06 19:53:08 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:11:09 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:11:09 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:11:09 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:11:09 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:11:09 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:11:09 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:11:09 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:11:10 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2476844
2025-08-09 17:11:11 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:11:11 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09 17:11:11 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:11:11 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:11:11 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:11:11 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:11:11 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476853; transaction id 3244
2025-08-09 17:11:11 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:11:11 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:11:11 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 17:11:11
2025-08-09 17:11:11 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:37:37 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:37:37 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:37:37 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:37:37 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:37:37 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:37:37 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:37:37 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:37:37 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2476862
2025-08-09 17:37:37 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:37:37 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09 17:37:37 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:37:37 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:37:37 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:37:37 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:37:37 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476871; transaction id 3244
2025-08-09 17:37:37 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:37:37 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:37:37 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 17:37:37
2025-08-09 17:37:37 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:37:37 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:37:37 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:37:37 0 [ERROR] Aborting
2025-08-09 17:37:44 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:37:44 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:37:44 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:37:44 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:37:44 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:37:44 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:37:44 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:37:44 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:37:44 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:37:44 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:37:44 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:37:44 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:37:44 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476880; transaction id 3244
2025-08-09 17:37:44 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:37:44 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:37:44 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:37:44 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:37:44 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:37:44 0 [ERROR] Aborting
2025-08-09 17:37:47 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:37:47 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:37:47 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:37:47 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:37:47 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:37:47 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:37:47 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:37:47 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:37:47 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:37:47 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:37:47 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:37:47 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:37:47 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476889; transaction id 3244
2025-08-09 17:37:47 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:37:47 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:37:47 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:37:47 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:37:47 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:37:47 0 [ERROR] Aborting
2025-08-09 17:37:56 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:37:56 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:37:56 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:37:56 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:37:56 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:37:56 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:37:56 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:37:56 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 17:37:56 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 17:37:56 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-09 17:37:56 0 [ERROR] Plugin 'InnoDB' registration as a STORAGE ENGINE failed.
2025-08-09 17:37:56 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:37:56 0 [ERROR] Unknown/unsupported storage engine: InnoDB
2025-08-09 17:37:56 0 [ERROR] Aborting
2025-08-09 17:37:56 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 17:37:56 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 17:37:56 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-09 17:37:56 0 [ERROR] Plugin 'InnoDB' registration as a STORAGE ENGINE failed.
2025-08-09 17:37:56 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:37:56 0 [ERROR] Unknown/unsupported storage engine: InnoDB
2025-08-09 17:37:56 0 [ERROR] Aborting
2025-08-09 17:37:56 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:37:56 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:37:56 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:37:56 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:37:56 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:37:57 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476898; transaction id 3244
2025-08-09 17:37:57 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:37:57 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:37:57 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:37:57 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:37:57 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:37:57 0 [ERROR] Aborting
2025-08-09 17:37:59 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:37:59 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:37:59 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:37:59 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:37:59 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:37:59 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:37:59 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:37:59 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:37:59 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:37:59 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:37:59 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:37:59 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:38:00 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476907; transaction id 3244
2025-08-09 17:38:00 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:38:00 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:38:00 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:38:00 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:38:00 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:38:00 0 [ERROR] Aborting
2025-08-09 17:38:38 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:38:38 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:38:38 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:38:38 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:38:38 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:38:38 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:38:38 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:38:38 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:38:38 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:38:38 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:38:38 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:38:38 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:38:38 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476916; transaction id 3244
2025-08-09 17:38:38 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:38:38 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:38:38 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:38:38 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:38:38 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:38:38 0 [ERROR] Aborting
2025-08-09 17:38:41 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:38:41 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:38:41 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:38:41 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:38:41 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:38:41 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:38:41 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:38:42 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:38:42 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:38:42 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:38:42 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:38:42 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:38:42 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476925; transaction id 3244
2025-08-09 17:38:42 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:38:42 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:38:42 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:38:42 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:38:42 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:38:42 0 [ERROR] Aborting
2025-08-09 17:38:45 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:38:45 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:38:45 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:38:45 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:38:45 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:38:45 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:38:45 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:38:45 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:38:45 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:38:45 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:38:45 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:38:45 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:38:45 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476934; transaction id 3244
2025-08-09 17:38:45 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:38:45 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:38:45 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:38:45 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:38:45 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:38:45 0 [ERROR] Aborting
2025-08-09 17:41:19 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:41:19 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:41:19 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:41:19 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:41:19 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:41:19 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:41:19 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:41:19 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:41:19 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:41:19 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:41:19 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:41:19 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:41:19 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476943; transaction id 3244
2025-08-09 17:41:19 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:41:19 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:41:19 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 17:41:19
2025-08-09 17:41:19 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:41:19 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:41:19 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:41:19 0 [ERROR] Aborting
2025-08-09 17:41:24 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:41:24 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:41:24 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:41:24 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:41:24 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:41:24 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:41:24 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:41:24 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:41:24 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:41:24 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:41:24 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:41:24 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:41:24 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476952; transaction id 3244
2025-08-09 17:41:24 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:41:24 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:41:24 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:41:24 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:41:24 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:41:24 0 [ERROR] Aborting
2025-08-09 17:41:54 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 17:41:54 0 [Note] InnoDB: Uses event mutexes
2025-08-09 17:41:54 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 17:41:54 0 [Note] InnoDB: Number of pools: 1
2025-08-09 17:41:54 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 17:41:54 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 17:41:54 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 17:41:54 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 17:41:54 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 17:41:54 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 17:41:54 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 17:41:54 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 17:41:54 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476961; transaction id 3244
2025-08-09 17:41:54 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 17:41:54 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 17:41:54 0 [Note] Server socket created on IP: '::'.
2025-08-09 17:41:54 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 17:41:54 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 17:41:54 0 [ERROR] Aborting
2025-08-09 18:33:05 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:05 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:05 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:05 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:05 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:05 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:05 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:06 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:33:06 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:33:06 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:33:06 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:33:06 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:33:06 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476970; transaction id 3244
2025-08-09 18:33:06 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:33:06 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:06 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 18:33:06
2025-08-09 18:33:06 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:33:06 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:33:06 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:33:06 0 [ERROR] Aborting
2025-08-09 18:33:17 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:17 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:17 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:17 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:17 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:17 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:17 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:17 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:33:17 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:33:17 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:33:17 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:33:17 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:33:17 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476979; transaction id 3244
2025-08-09 18:33:17 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:33:17 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:17 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:33:17 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:33:17 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:33:17 0 [ERROR] Aborting
2025-08-09 18:33:21 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:21 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:21 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:21 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:21 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:21 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:21 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:21 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 18:33:21 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 18:33:21 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-09 18:33:21 0 [ERROR] Plugin 'InnoDB' registration as a STORAGE ENGINE failed.
2025-08-09 18:33:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:21 0 [ERROR] Unknown/unsupported storage engine: InnoDB
2025-08-09 18:33:21 0 [ERROR] Aborting
2025-08-09 18:33:21 0 [Note] InnoDB: 128 out of 2102285 -r0oll8b-a0c9k  1s8e:g3m3e:n21t s0  a[reE RaRcOtRi]v eI.n
noDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 18:33:21 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 18:33:21 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-09 18:33:21 0 [2No0t2e] 5I-nn0o8D-B0:9  C1r8e:a3t3i:n2g1  s0h a[rEeRdR OtRa]b lPelsupgaicne  'fIonrn otDeBm'p orreagriys ttraabtlieosn
 as a STORAGE ENGINE failed.
2025-08-09 18:33:21 0 [Note] InnoDB: Setti2ng 0fil2e5 -'0C8:-\f0i9l e1\8m:y3s3q:l2\1d a0t a[\Niobttem]p 1P'l usgiizne  'tFoE E1D2B AMCBK.'  Pihsy sdiicsaalbllye dw.r
iting the file full; Please wait ...
2025-08-09 18:33:21 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:33:2025-082-019  01 8[:E3R3R:O2R1]  0U n[kNnootwen]/ uInnsnuopDpBo:r Waiting tfoerd  psutrogrea gteo  esntgairnte
: InnoDB
2025-08-09 18:33:21 0 [ERROR] Aborting
2025-08-09 18:33:21 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 18:33:21 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-09 18:33:21 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-09 18:33:21 0 [ERROR] Plugin 'InnoDB' registration as a STORAGE ENGINE failed.
2025-08-09 18:33:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:21 0 [ERROR] Unknown/unsupported storage engine: InnoDB
2025-08-09 18:33:21 0 [ERROR] Aborting
2025-08-09 18:33:21 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476988; transaction id 3244
2025-08-09 18:33:21 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:33:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:21 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:33:21 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:33:21 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:33:21 0 [ERROR] Aborting
2025-08-09 18:33:22 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:22 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:22 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:22 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:22 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:22 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:22 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:22 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must2 0b2e5 -w0r8i-t0a9 b1l8e:2
303:2252- 008 [-E0RRO9R ]1 8I:n3n3o:D22 0B [ER2R:0O 2RT5]h- eI0 ni8nn-on0Do9Bd :b1 8_T:sh3ye3s :ti2ne2n mo0  dd[baE_tRsaRy OsfRti]le emI n 'ndioabDtdBaa: t faTi1hl'ee   mi'unisnbtod dabbte_a s1wy'rs itmteuamsb tld eab
te wari tfaibllee 
'ibdata1' must be writable
2025-082-00295 -108:823-0302:952 -210 808: -3[039E: R2R12O8 R:0]3  3[I:En2nR2oR ODR0]B  :[I nEnToRhDReBO:  RiT]nh neoP dliubng_nsioynsd tbe_'msI yndsnatoteaDm  daftBia'l  efi in'liei tb' difabutdnaac1tt'ai 1om'nu  smrtue stbteu  brwenr eiwdtr aiebtrlaerb
olr.e

22002255--020808-2-0509-9 0 1818-8:0:3933 3:1:2822:2 3 030 : [2[E2ER RR0RO OR[R]E] R PRPlOluRug]gi inPn l 'u'IgIninnnno oD'DBIB'n' n iornDeiBgt'i  sftiurnnaicttti iofonun n acrste itaou nrS nTreOedRt AueGrrEnr eoEdrN .Ge
IrNrEo rf.a
iled.
2025-08-09 182:03235:-2028 -00 [9E R1R8O:R3]3 2:P02l22u5 g-0i0 n8[ -E'0RI9Rn On1Ro8]D: B3P'3l :ur2ge2ig ni0 s 't[IrNnaonttoieDo]Bn ' P alrsue ggaii nsS tT'rOFaREtAEiGDoEBn A ECaNKsG' I aNi EsS  TfdOaiRisAlaGebEdl .eE
dN.G
INE failed.
2025-08-09 18:33:22 02 [02N5o-t0e8]- 0P9l u1gi8n: 3'3F:E2E2D B0A C[KN'o ties]  dPilsuagbilne d'.F
EEDBACK' is disabled.
222000222555--0-080-808-9- 001998  :113883:::332333:: 22033   [00E  R[[REEORRRR]RO ORUR]n] k UnUnonkwknnn/oouwnwnsn/u/upunpnsousrputppepodor rttseetddo  rssattgooerr aaeggneeg  ieennnegg:i innIee:n :Inn nooIDDBnB
n
oDB
2025-08-09 18:33:23 0 [ERROR] Aborting
202250-205-088-0-90 91 81:338:23 0 :[3E3R:ROR2]3  A0b o[rEtRiRnOR]g 
Aborting
22025-08-09 18:33:25 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:25 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:25 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:25 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:25 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:25 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:25 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:25 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:33:25 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09 18:33:25 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:33:25 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:33:25 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:33:25 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:33:25 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476988; transaction id 3244
2025-08-09 18:33:25 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:33:25 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:25 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:33:25 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:33:25 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:33:25 0 [ERROR] Aborting
2025-08-09 18:33:27 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:27 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:27 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:27 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:27 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:27 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:27 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:27 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:33:27 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09 18:33:27 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:33:27 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:33:27 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:33:27 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:33:27 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476988; transaction id 3244
2025-08-09 18:33:27 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:33:27 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:27 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:33:27 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:33:27 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:33:27 0 [ERROR] Aborting
2025-08-09 18:33:50 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:33:50 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:33:50 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:33:50 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:33:50 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:33:50 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:33:50 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:33:51 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:33:51 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:33:51 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:33:51 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:33:51 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:33:51 0 [Note] InnoDB: 10.4.22 started; log sequence number 2476997; transaction id 3244
2025-08-09 18:33:52 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:33:52 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:33:52 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 18:33:52
2025-08-09 18:33:52 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:33:52 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:33:52 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:33:52 0 [ERROR] Aborting
2025-08-09 18:47:57 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:47:57 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:47:57 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:47:57 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:47:57 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:47:57 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:47:57 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:47:58 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:47:58 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:47:58 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:47:58 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:47:58 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:47:58 0 [Note] InnoDB: 10.4.22 started; log sequence number 2477006; transaction id 3244
2025-08-09 18:47:58 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:47:58 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:47:58 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 18:47:58
2025-08-09 18:47:58 0 [Note] Server socket created on IP: '::'.
2025-08-09 18:47:58 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-09 18:47:58 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-09 18:47:58 0 [ERROR] Aborting
2025-08-09 18:50:55 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 18:50:55 0 [Note] InnoDB: Uses event mutexes
2025-08-09 18:50:55 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 18:50:55 0 [Note] InnoDB: Number of pools: 1
2025-08-09 18:50:55 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 18:50:55 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 18:50:55 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 18:50:55 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 18:50:55 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 18:50:55 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 18:50:55 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 18:50:55 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 18:50:55 0 [Note] InnoDB: 10.4.22 started; log sequence number 2477015; transaction id 3244
2025-08-09 18:50:55 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 18:50:55 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 18:50:55 0 [Note] Server socket created on IP: '::'.
2025-08-09 22:05:21 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-09 22:05:21 0 [Note] InnoDB: Uses event mutexes
2025-08-09 22:05:21 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-09 22:05:21 0 [Note] InnoDB: Number of pools: 1
2025-08-09 22:05:21 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-09 22:05:21 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-09 22:05:21 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-09 22:05:21 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2547131
2025-08-09 22:05:22 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-09 22:05:22 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-09 22:05:22 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-09 22:05:22 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-09 22:05:22 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-09 22:05:22 0 [Note] InnoDB: Waiting for purge to start
2025-08-09 22:05:22 0 [Note] InnoDB: 10.4.22 started; log sequence number 2547140; transaction id 3563
2025-08-09 22:05:22 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-09 22:05:22 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-09 22:05:22 0 [Note] InnoDB: Buffer pool(s) load completed at 250809 22:05:22
2025-08-09 22:05:22 0 [Note] Server socket created on IP: '::'.
2025-08-15 14:57:41 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-15 14:57:41 0 [Note] InnoDB: Uses event mutexes
2025-08-15 14:57:41 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-15 14:57:41 0 [Note] InnoDB: Number of pools: 1
2025-08-15 14:57:41 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-15 14:57:41 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-15 14:57:41 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-15 14:57:41 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2547149
2025-08-15 14:57:42 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-15 14:57:42 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-15 14:57:42 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-15 14:57:42 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-15 14:57:42 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-15 14:57:42 0 [Note] InnoDB: Waiting for purge to start
2025-08-15 14:57:42 0 [Note] InnoDB: 10.4.22 started; log sequence number 2547158; transaction id 3563
2025-08-15 14:57:42 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-15 14:57:42 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-15 14:57:42 0 [Note] InnoDB: Buffer pool(s) load completed at 250815 14:57:42
2025-08-15 14:57:42 0 [Note] Server socket created on IP: '::'.
2025-08-15 14:57:46 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-15 14:57:46 0 [Note] InnoDB: Uses event mutexes
2025-08-15 14:57:46 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-15 14:57:46 0 [Note] InnoDB: Number of pools: 1
2025-08-15 14:57:46 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-15 14:57:46 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-15 14:57:46 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-15 14:57:46 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2547149
2025-08-15 14:57:46 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-15 14:57:46 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-15 14:57:46 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-15 14:57:46 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-15 14:57:46 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-15 14:57:46 0 [Note] InnoDB: Waiting for purge to start
2025-08-15 14:57:46 0 [Note] InnoDB: 10.4.22 started; log sequence number 2547158; transaction id 3563
2025-08-15 14:57:46 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-15 14:57:46 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-15 14:57:46 0 [Note] Server socket created on IP: '::'.
2025-08-17  3:22:39 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-17  3:22:39 0 [Note] InnoDB: Uses event mutexes
2025-08-17  3:22:39 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-17  3:22:39 0 [Note] InnoDB: Number of pools: 1
2025-08-17  3:22:39 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-17  3:22:39 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-17  3:22:39 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-17  3:22:39 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2637251
2025-08-17  3:22:40 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-17  3:22:40 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-17  3:22:40 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-17  3:22:40 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-17  3:22:40 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-17  3:22:40 0 [Note] InnoDB: Waiting for purge to start
2025-08-17  3:22:41 0 [Note] InnoDB: 10.4.22 started; log sequence number 2637260; transaction id 3809
2025-08-17  3:22:41 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-17  3:22:41 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  3:22:41 0 [Note] InnoDB: Buffer pool(s) load completed at 250817  3:22:41
2025-08-17  3:22:41 0 [Note] Server socket created on IP: '::'.
2025-08-17  3:47:25 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-17  3:47:25 0 [Note] InnoDB: Uses event mutexes
2025-08-17  3:47:25 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-17  3:47:25 0 [Note] InnoDB: Number of pools: 1
2025-08-17  3:47:25 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-17  3:47:25 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-17  3:47:25 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-17  3:47:25 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2637269
2025-08-17  3:47:25 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-17  3:47:25 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-17  3:47:25 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-17  3:47:25 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-17  3:47:25 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-17  3:47:25 0 [Note] InnoDB: Waiting for purge to start
2025-08-17  3:47:25 0 [Note] InnoDB: 10.4.22 started; log sequence number 2637278; transaction id 3809
2025-08-17  3:47:25 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-17  3:47:25 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  3:47:25 0 [Note] InnoDB: Buffer pool(s) load completed at 250817  3:47:25
2025-08-17  3:47:25 0 [Note] Server socket created on IP: '::'.
2025-08-17  3:47:39 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-17  3:47:39 0 [Note] InnoDB: Uses event mutexes
2025-08-17  3:47:39 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-17  3:47:39 0 [Note] InnoDB: Number of pools: 1
2025-08-17  3:47:39 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-17  3:47:39 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-17  3:47:39 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-17  3:47:39 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2637287
2025-08-17  3:47:39 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-17  3:47:39 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-17  3:47:39 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-17  3:47:39 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-17  3:47:39 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-17  3:47:39 0 [Note] InnoDB: Waiting for purge to start
2025-08-17  3:47:39 0 [Note] InnoDB: 10.4.22 started; log sequence number 2637296; transaction id 3809
2025-08-17  3:47:39 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-17  3:47:39 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  3:47:39 0 [Note] Server socket created on IP: '::'.
2025-08-17  3:54:36 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-17  3:54:36 0 [Note] InnoDB: Uses event mutexes
2025-08-17  3:54:36 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-17  3:54:36 0 [Note] InnoDB: Number of pools: 1
2025-08-17  3:54:36 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-17  3:54:36 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-17  3:54:36 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-17  3:54:36 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-17  3:54:36 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-17  3:54:36 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-17  3:54:36 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-17  3:54:36 0 [Note] InnoDB: Waiting for purge to start
2025-08-17  3:54:36 0 [Note] InnoDB: 10.4.22 started; log sequence number 2637339; transaction id 3809
2025-08-17  3:54:36 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-17  3:54:36 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  3:54:36 0 [Note] InnoDB: Buffer pool(s) load completed at 250817  3:54:36
2025-08-17  3:54:36 0 [Note] Server socket created on IP: '::'.
2025-08-17  3:54:36 0 [Note] Reading of all Master_info entries succeeded
2025-08-17  3:54:36 0 [Note] Added new Master_info '' to hash table
2025-08-17  3:54:36 0 [Note] mysql\bin\mysqld.exe: ready for connections.
Version: '10.4.22-MariaDB'  socket: ''  port: 3306  mariadb.org binary distribution
2025-08-17  4:07:20 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-17  4:07:20 0 [Note] InnoDB: Uses event mutexes
2025-08-17  4:07:20 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-17  4:07:20 0 [Note] InnoDB: Number of pools: 1
2025-08-17  4:07:20 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-17  4:07:20 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-17  4:07:20 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-17  4:07:20 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2637339
2025-08-17  4:07:21 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-17  4:07:21 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-17  4:07:21 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-17  4:07:21 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-17  4:07:21 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-17  4:07:21 0 [Note] InnoDB: Waiting for purge to start
2025-08-17  4:07:21 0 [Note] InnoDB: 10.4.22 started; log sequence number 2637348; transaction id 3809
2025-08-17  4:07:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  4:07:21 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-17  4:07:21 0 [Note] Server socket created on IP: '::'.
2025-08-17  4:07:21 0 [Note] InnoDB: Buffer pool(s) load completed at 250817  4:07:21
2025-08-17  4:07:22 0 [Note] Reading of all Master_info entries succeeded
2025-08-17  4:07:22 0 [Note] Added new Master_info '' to hash table
2025-08-17  4:07:22 0 [Note] mysql\bin\mysqld.exe: ready for connections.
Version: '10.4.22-MariaDB'  socket: ''  port: 3306  mariadb.org binary distribution
2025-08-17  4:26:07 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-17  4:26:07 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-17  4:26:07 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-17  4:26:07 0 [ERROR] Plugin 'InnoDB' registration as a STORAGE ENGINE failed.
2025-08-17  4:26:07 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  4:26:07 0 [ERROR] Unknown/unsupported storage engine: InnoDB
2025-08-17  4:26:07 0 [ERROR] Aborting
2025-08-17  4:41:47 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-17  4:41:47 0 [ERROR] InnoDB: The innodb_system data file 'ibdata1' must be writable
2025-08-17  4:41:47 0 [ERROR] Plugin 'InnoDB' init function returned error.
2025-08-17  4:41:47 0 [ERROR] Plugin 'InnoDB' registration as a STORAGE ENGINE failed.
2025-08-17  4:41:47 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-17  4:41:47 0 [ERROR] Unknown/unsupported storage engine: InnoDB
2025-08-17  4:41:47 0 [ERROR] Aborting
2025-08-17  5:03:31 72 [Warning] Aborted connection 72 to db: 'eschool' user: 'root' host: 'localhost' (Got an error reading communication packets)
2025-08-19 21:18:15 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-19 21:18:15 0 [Note] InnoDB: Uses event mutexes
2025-08-19 21:18:15 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-19 21:18:15 0 [Note] InnoDB: Number of pools: 1
2025-08-19 21:18:15 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-19 21:18:15 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-19 21:18:15 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-19 21:18:15 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=12562504
2025-08-19 21:18:17 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-19 21:18:17 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-19 21:18:17 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-19 21:18:17 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-19 21:18:17 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-19 21:18:17 0 [Note] InnoDB: Waiting for purge to start
2025-08-19 21:18:17 0 [Note] InnoDB: 10.4.22 started; log sequence number 12562513; transaction id 16622
2025-08-19 21:18:17 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-19 21:18:17 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-19 21:18:17 0 [Note] InnoDB: Buffer pool(s) load completed at 250819 21:18:17
2025-08-19 21:18:17 0 [Note] Server socket created on IP: '::'.
2025-08-19 21:34:12 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-19 21:34:12 0 [Note] InnoDB: Uses event mutexes
2025-08-19 21:34:12 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-19 21:34:12 0 [Note] InnoDB: Number of pools: 1
2025-08-19 21:34:12 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-19 21:34:12 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-19 21:34:12 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-19 21:34:12 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=12562522
2025-08-19 21:34:13 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-19 21:34:13 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-19 21:34:13 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-19 21:34:13 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-19 21:34:13 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-19 21:34:13 0 [Note] InnoDB: Waiting for purge to start
2025-08-19 21:34:13 0 [Note] InnoDB: 10.4.22 started; log sequence number 12562531; transaction id 16622
2025-08-19 21:34:13 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-19 21:34:13 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-19 21:34:13 0 [Note] InnoDB: Buffer pool(s) load completed at 250819 21:34:13
2025-08-19 21:34:13 0 [Note] Server socket created on IP: '::'.
2025-08-19 21:34:13 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-19 21:34:13 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-19 21:34:13 0 [ERROR] Aborting
2025-08-19 21:35:20 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-19 21:35:20 0 [Note] InnoDB: Uses event mutexes
2025-08-19 21:35:20 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-19 21:35:20 0 [Note] InnoDB: Number of pools: 1
2025-08-19 21:35:20 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-19 21:35:21 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-19 21:35:21 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-19 21:35:21 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-19 21:35:21 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-19 21:35:21 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-19 21:35:21 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-19 21:35:21 0 [Note] InnoDB: Waiting for purge to start
2025-08-19 21:35:21 0 [Note] InnoDB: 10.4.22 started; log sequence number 12562540; transaction id 16622
2025-08-19 21:35:21 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-19 21:35:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-19 21:35:21 0 [Note] Server socket created on IP: '::'.
2025-08-19 21:35:21 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-19 21:35:21 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-19 21:35:21 0 [ERROR] Aborting
2025-08-19 21:35:36 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-19 21:35:36 0 [Note] InnoDB: Uses event mutexes
2025-08-19 21:35:36 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-19 21:35:36 0 [Note] InnoDB: Number of pools: 1
2025-08-19 21:35:36 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-19 21:35:36 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-19 21:35:36 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-19 21:35:36 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-19 21:35:36 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-19 21:35:36 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-19 21:35:36 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-19 21:35:36 0 [Note] InnoDB: Waiting for purge to start
2025-08-19 21:35:36 0 [Note] InnoDB: 10.4.22 started; log sequence number 12562549; transaction id 16622
2025-08-19 21:35:36 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-19 21:35:36 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-19 21:35:36 0 [Note] Server socket created on IP: '::'.
2025-08-19 21:35:36 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-08-19 21:35:36 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-08-19 21:35:36 0 [ERROR] Aborting
2025-08-19 21:36:57 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-19 21:36:57 0 [Note] InnoDB: Uses event mutexes
2025-08-19 21:36:57 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-19 21:36:57 0 [Note] InnoDB: Number of pools: 1
2025-08-19 21:36:57 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-19 21:36:57 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-19 21:36:57 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-19 21:36:58 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-19 21:36:58 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-19 21:36:58 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-19 21:36:58 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-19 21:36:58 0 [Note] InnoDB: Waiting for purge to start
2025-08-19 21:36:58 0 [Note] InnoDB: 10.4.22 started; log sequence number 12562558; transaction id 16622
2025-08-19 21:36:58 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-19 21:36:58 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-19 21:36:58 0 [Note] Server socket created on IP: '::'.
2025-08-21  3:55:26 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-21  3:55:26 0 [Note] InnoDB: Uses event mutexes
2025-08-21  3:55:26 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-21  3:55:26 0 [Note] InnoDB: Number of pools: 1
2025-08-21  3:55:26 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-21  3:55:26 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-21  3:55:26 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-21  3:55:26 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=12780661
2025-08-21  3:55:27 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-21  3:55:27 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-21  3:55:27 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-21  3:55:27 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-21  3:55:27 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-21  3:55:27 0 [Note] InnoDB: Waiting for purge to start
2025-08-21  3:55:27 0 [Note] InnoDB: 10.4.22 started; log sequence number 12780670; transaction id 16886
2025-08-21  3:55:27 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-21  3:55:27 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-21  3:55:27 0 [Note] InnoDB: Buffer pool(s) load completed at 250821  3:55:27
2025-08-21  3:55:28 0 [Note] Server socket created on IP: '::'.
2025-08-22 13:21:19 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-22 13:21:19 0 [Note] InnoDB: Uses event mutexes
2025-08-22 13:21:19 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-22 13:21:19 0 [Note] InnoDB: Number of pools: 1
2025-08-22 13:21:19 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-22 13:21:19 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-22 13:21:19 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-22 13:21:19 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14631468
2025-08-22 13:21:21 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-22 13:21:21 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-22 13:21:21 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-22 13:21:21 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-22 13:21:21 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-22 13:21:21 0 [Note] InnoDB: Waiting for purge to start
2025-08-22 13:21:22 0 [Note] InnoDB: 10.4.22 started; log sequence number 14631477; transaction id 19842
2025-08-22 13:21:22 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-22 13:21:22 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-22 13:21:22 0 [Note] InnoDB: Buffer pool(s) load completed at 250822 13:21:22
2025-08-22 13:21:22 0 [Note] Server socket created on IP: '::'.
2025-08-24 15:22:50 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-24 15:22:50 0 [Note] InnoDB: Uses event mutexes
2025-08-24 15:22:50 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-24 15:22:50 0 [Note] InnoDB: Number of pools: 1
2025-08-24 15:22:50 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-24 15:22:50 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-24 15:22:50 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-24 15:22:50 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14631486
2025-08-24 15:22:52 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-24 15:22:52 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-24 15:22:52 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-24 15:22:52 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-24 15:22:52 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-24 15:22:52 0 [Note] InnoDB: Waiting for purge to start
2025-08-24 15:22:52 0 [Note] InnoDB: 10.4.22 started; log sequence number 14631495; transaction id 19842
2025-08-24 15:22:52 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-24 15:22:52 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-24 15:22:52 0 [Note] InnoDB: Buffer pool(s) load completed at 250824 15:22:52
2025-08-24 15:22:52 0 [Note] Server socket created on IP: '::'.
2025-08-24 16:29:25 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-24 16:29:25 0 [Note] InnoDB: Uses event mutexes
2025-08-24 16:29:25 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-24 16:29:25 0 [Note] InnoDB: Number of pools: 1
2025-08-24 16:29:25 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-24 16:29:25 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-24 16:29:25 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-24 16:29:25 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14634120
2025-08-24 16:29:25 0 [Note] InnoDB: Starting final batch to recover 7 pages from redo log.
2025-08-24 16:29:25 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-24 16:29:25 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-24 16:29:25 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-24 16:29:25 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-24 16:29:25 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-24 16:29:25 0 [Note] InnoDB: Waiting for purge to start
2025-08-24 16:29:25 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635208; transaction id 19875
2025-08-24 16:29:25 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-24 16:29:25 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-24 16:29:25 0 [Note] InnoDB: Buffer pool(s) load completed at 250824 16:29:25
2025-08-24 16:29:25 0 [Note] Server socket created on IP: '::'.
2025-08-24 16:29:35 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-24 16:29:35 0 [Note] InnoDB: Uses event mutexes
2025-08-24 16:29:35 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-24 16:29:35 0 [Note] InnoDB: Number of pools: 1
2025-08-24 16:29:35 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-24 16:29:35 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-24 16:29:35 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-24 16:29:35 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14632600
2025-08-24 16:29:35 0 [Note] InnoDB: Starting final batch to recover 7 pages from redo log.
2025-08-24 16:29:36 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-24 16:29:36 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-24 16:29:36 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-24 16:29:36 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-24 16:29:36 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-24 16:29:36 0 [Note] InnoDB: Waiting for purge to start
2025-08-24 16:29:36 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635208; transaction id 19875
2025-08-24 16:29:36 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-24 16:29:36 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-24 16:29:36 0 [Note] Server socket created on IP: '::'.
2025-08-26 15:18:40 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-26 15:18:40 0 [Note] InnoDB: Uses event mutexes
2025-08-26 15:18:40 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-26 15:18:40 0 [Note] InnoDB: Number of pools: 1
2025-08-26 15:18:40 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-26 15:18:40 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-26 15:18:40 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-26 15:18:40 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14635543
2025-08-26 15:18:42 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-26 15:18:42 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-26 15:18:42 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-26 15:18:42 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-26 15:18:42 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-26 15:18:42 0 [Note] InnoDB: Waiting for purge to start
2025-08-26 15:18:42 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635552; transaction id 19875
2025-08-26 15:18:42 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-26 15:18:42 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-26 15:18:42 0 [Note] InnoDB: Buffer pool(s) load completed at 250826 15:18:42
2025-08-26 15:18:42 0 [Note] Server socket created on IP: '::'.
2025-08-26 15:20:05 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-26 15:20:05 0 [Note] InnoDB: Uses event mutexes
2025-08-26 15:20:05 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-26 15:20:05 0 [Note] InnoDB: Number of pools: 1
2025-08-26 15:20:05 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-26 15:20:05 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-26 15:20:05 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-26 15:20:05 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14635561
2025-08-26 15:20:05 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-26 15:20:05 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-26 15:20:05 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-26 15:20:06 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-26 15:20:06 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-26 15:20:06 0 [Note] InnoDB: Waiting for purge to start
2025-08-26 15:20:06 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635570; transaction id 19875
2025-08-26 15:20:06 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-26 15:20:06 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-26 15:20:06 0 [Note] Server socket created on IP: '::'.
2025-08-26 22:58:23 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-26 22:58:23 0 [Note] InnoDB: Uses event mutexes
2025-08-26 22:58:23 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-26 22:58:23 0 [Note] InnoDB: Number of pools: 1
2025-08-26 22:58:23 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-26 22:58:23 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-26 22:58:23 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-26 22:58:24 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14635561
2025-08-26 22:58:26 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-26 22:58:26 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-26 22:58:26 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-26 22:58:26 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-26 22:58:26 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-26 22:58:26 0 [Note] InnoDB: Waiting for purge to start
2025-08-26 22:58:26 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635570; transaction id 19875
2025-08-26 22:58:26 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-26 22:58:26 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-26 22:58:26 0 [Note] InnoDB: Buffer pool(s) load completed at 250826 22:58:26
2025-08-26 22:58:26 0 [Note] Server socket created on IP: '::'.
2025-08-26 22:58:29 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-26 22:58:29 0 [Note] InnoDB: Uses event mutexes
2025-08-26 22:58:29 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-26 22:58:29 0 [Note] InnoDB: Number of pools: 1
2025-08-26 22:58:29 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-26 22:58:29 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-26 22:58:29 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-26 22:58:29 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14635561
2025-08-26 22:58:29 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-26 22:58:29 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-26 22:58:29 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-26 22:58:29 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-26 22:58:29 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-26 22:58:29 0 [Note] InnoDB: Waiting for purge to start
2025-08-26 22:58:29 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635570; transaction id 19875
2025-08-26 22:58:29 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-26 22:58:29 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-26 22:58:29 0 [Note] Server socket created on IP: '::'.
2025-08-26 22:58:29 0 [Note] InnoDB: Buffer pool(s) load completed at 250826 22:58:29
2025-08-26 22:58:30 0 [Note] Reading of all Master_info entries succeeded
2025-08-26 22:58:30 0 [Note] Added new Master_info '' to hash table
2025-08-26 22:58:30 0 [Note] c:\file\mysql\bin\mysqld.exe: ready for connections.
Version: '10.4.22-MariaDB'  socket: ''  port: 3306  mariadb.org binary distribution
2025-08-27 20:53:06 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-27 20:53:06 0 [Note] InnoDB: Uses event mutexes
2025-08-27 20:53:06 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-27 20:53:06 0 [Note] InnoDB: Number of pools: 1
2025-08-27 20:53:06 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-27 20:53:06 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-27 20:53:06 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-27 20:53:06 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14635579
2025-08-27 20:53:07 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-27 20:53:07 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-27 20:53:07 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-27 20:53:07 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-27 20:53:07 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-27 20:53:07 0 [Note] InnoDB: Waiting for purge to start
2025-08-27 20:53:08 0 [Note] InnoDB: 10.4.22 started; log sequence number 14635588; transaction id 19875
2025-08-27 20:53:08 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-27 20:53:08 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-27 20:53:08 0 [Note] InnoDB: Buffer pool(s) load completed at 250827 20:53:08
2025-08-27 20:53:08 0 [Note] Server socket created on IP: '::'.
2025-08-30 13:56:04 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-30 13:56:04 0 [Note] InnoDB: Uses event mutexes
2025-08-30 13:56:04 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-30 13:56:04 0 [Note] InnoDB: Number of pools: 1
2025-08-30 13:56:04 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-30 13:56:04 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-30 13:56:04 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-30 13:56:04 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14657091
2025-08-30 13:56:05 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-30 13:56:05 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-30 13:56:05 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-30 13:56:05 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-30 13:56:05 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-30 13:56:05 0 [Note] InnoDB: Waiting for purge to start
2025-08-30 13:56:05 0 [Note] InnoDB: 10.4.22 started; log sequence number 14657100; transaction id 20011
2025-08-30 13:56:05 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-30 13:56:05 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-30 13:56:05 0 [Note] InnoDB: Buffer pool(s) load completed at 250830 13:56:05
2025-08-30 13:56:05 0 [Note] Server socket created on IP: '::'.
2025-08-30 21:33:57 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-08-30 21:33:57 0 [Note] InnoDB: Uses event mutexes
2025-08-30 21:33:57 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-08-30 21:33:57 0 [Note] InnoDB: Number of pools: 1
2025-08-30 21:33:57 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-08-30 21:33:57 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-08-30 21:33:57 0 [Note] InnoDB: Completed initialization of buffer pool
2025-08-30 21:33:57 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14657109
2025-08-30 21:33:59 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-08-30 21:33:59 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-08-30 21:33:59 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-08-30 21:33:59 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-08-30 21:33:59 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-08-30 21:33:59 0 [Note] InnoDB: Waiting for purge to start
2025-08-30 21:33:59 0 [Note] InnoDB: 10.4.22 started; log sequence number 14657118; transaction id 20011
2025-08-30 21:33:59 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-08-30 21:33:59 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-08-30 21:33:59 0 [Note] InnoDB: Buffer pool(s) load completed at 250830 21:33:59
2025-08-30 21:33:59 0 [Note] Server socket created on IP: '::'.
2025-09-01 16:05:42 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-01 16:05:42 0 [Note] InnoDB: Uses event mutexes
2025-09-01 16:05:42 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-01 16:05:42 0 [Note] InnoDB: Number of pools: 1
2025-09-01 16:05:42 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-01 16:05:42 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-01 16:05:42 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-01 16:05:42 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14657866
2025-09-01 16:05:43 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-01 16:05:43 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-01 16:05:43 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-01 16:05:43 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-01 16:05:43 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-01 16:05:43 0 [Note] InnoDB: Waiting for purge to start
2025-09-01 16:05:43 0 [Note] InnoDB: 10.4.22 started; log sequence number 14657875; transaction id 20037
2025-09-01 16:05:43 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-01 16:05:43 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-01 16:05:43 0 [Note] InnoDB: Buffer pool(s) load completed at 250901 16:05:43
2025-09-01 16:05:43 0 [Note] Server socket created on IP: '::'.
2025-09-08 23:27:19 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-08 23:27:19 0 [Note] InnoDB: Uses event mutexes
2025-09-08 23:27:19 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-08 23:27:19 0 [Note] InnoDB: Number of pools: 1
2025-09-08 23:27:19 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-08 23:27:19 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-08 23:27:19 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-08 23:27:19 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14784246
2025-09-08 23:27:19 0 [Note] InnoDB: Starting final batch to recover 8 pages from redo log.
2025-09-08 23:27:21 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-08 23:27:21 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-08 23:27:21 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-08 23:27:21 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-08 23:27:21 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-08 23:27:21 0 [Note] InnoDB: Waiting for purge to start
2025-09-08 23:27:21 0 [Note] InnoDB: 10.4.22 started; log sequence number 14785855; transaction id 20609
2025-09-08 23:27:21 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-08 23:27:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-08 23:27:21 0 [Note] InnoDB: Buffer pool(s) load completed at 250908 23:27:21
2025-09-08 23:27:21 0 [Note] Server socket created on IP: '::'.
2025-09-09  0:31:49 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-09  0:31:49 0 [Note] InnoDB: Uses event mutexes
2025-09-09  0:31:49 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-09  0:31:49 0 [Note] InnoDB: Number of pools: 1
2025-09-09  0:31:49 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-09  0:31:49 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-09  0:31:49 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-09  0:31:49 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786399
2025-09-09  0:31:49 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-09  0:31:49 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-09  0:31:49 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-09  0:31:49 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-09  0:31:49 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-09  0:31:49 0 [Note] InnoDB: Waiting for purge to start
2025-09-09  0:31:50 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786408; transaction id 20609
2025-09-09  0:31:50 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-09  0:31:50 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-09  0:31:50 0 [Note] Server socket created on IP: '::'.
2025-09-09  0:32:31 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-09  0:32:31 0 [Note] InnoDB: Uses event mutexes
2025-09-09  0:32:31 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-09  0:32:31 0 [Note] InnoDB: Number of pools: 1
2025-09-09  0:32:31 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-09  0:32:31 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-09  0:32:31 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-09  0:32:31 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786417
2025-09-09  0:32:31 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-09  0:32:31 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-09  0:32:31 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-09  0:32:31 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-09  0:32:31 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-09  0:32:31 0 [Note] InnoDB: Waiting for purge to start
2025-09-09  0:32:31 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786426; transaction id 20609
2025-09-09  0:32:31 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-09  0:32:31 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-09  0:32:31 0 [Note] Server socket created on IP: '::'.
2025-09-09  0:48:03 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-09  0:48:03 0 [Note] InnoDB: Uses event mutexes
2025-09-09  0:48:03 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-09  0:48:03 0 [Note] InnoDB: Number of pools: 1
2025-09-09  0:48:03 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-09  0:48:03 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-09  0:48:03 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-09  0:48:03 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786435
2025-09-09  0:48:04 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-09  0:48:04 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-09  0:48:04 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-09  0:48:04 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-09  0:48:04 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-09  0:48:04 0 [Note] InnoDB: Waiting for purge to start
2025-09-09  0:48:04 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786444; transaction id 20609
2025-09-09  0:48:04 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-09  0:48:04 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-09  0:48:04 0 [Note] InnoDB: Buffer pool(s) load completed at 250909  0:48:04
2025-09-09  0:48:04 0 [Note] Server socket created on IP: '::'.
2025-09-09  0:48:15 0 [Note] mysqld.exe: Aria engine: starting recovery
recovered pages: 0% 100% (0.0 seconds); 
2025-09-09  0:48:15 0 [Note] mysqld.exe: Aria engine: recovery done
2025-09-09  0:48:15 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-09  0:48:15 0 [Note] InnoDB: Uses event mutexes
2025-09-09  0:48:15 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-09  0:48:15 0 [Note] InnoDB: Number of pools: 1
2025-09-09  0:48:15 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-09  0:48:15 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-09  0:48:15 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-09  0:48:15 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786453
2025-09-09  0:48:15 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-09  0:48:15 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-09  0:48:15 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-09  0:48:15 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-09  0:48:15 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-09  0:48:15 0 [Note] InnoDB: Waiting for purge to start
2025-09-09  0:48:16 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786462; transaction id 20609
2025-09-09  0:48:16 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-09  0:48:16 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-09  0:48:16 0 [Note] Server socket created on IP: '::'.
2025-09-09  0:54:44 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-09  0:54:44 0 [Note] InnoDB: Uses event mutexes
2025-09-09  0:54:44 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-09  0:54:44 0 [Note] InnoDB: Number of pools: 1
2025-09-09  0:54:44 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-09  0:54:44 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-09  0:54:44 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-09  0:54:44 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786471
2025-09-09  0:54:46 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-09  0:54:46 0 [Note] InnoDB: Uses event mutexes
2025-09-09  0:54:46 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-09  0:54:46 0 [Note] InnoDB: Number of pools: 1
2025-09-09  0:54:46 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-09  0:54:46 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-09  0:54:46 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-09  0:54:46 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786471
2025-09-09  0:54:47 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-09  0:54:47 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-09  0:54:47 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-09  0:54:47 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-09  0:54:47 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-09  0:54:47 0 [Note] InnoDB: Waiting for purge to start
2025-09-09  0:54:47 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786480; transaction id 20609
2025-09-09  0:54:47 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-09  0:54:47 0 [Note] Server socket created on IP: '::'.
2025-09-09  0:54:47 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-10 18:36:24 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-10 18:36:24 0 [Note] InnoDB: Uses event mutexes
2025-09-10 18:36:24 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-10 18:36:24 0 [Note] InnoDB: Number of pools: 1
2025-09-10 18:36:24 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-10 18:36:24 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-10 18:36:24 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-10 18:36:24 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786489
2025-09-10 18:36:26 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-10 18:36:26 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-10 18:36:26 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-10 18:36:26 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-10 18:36:26 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-10 18:36:26 0 [Note] InnoDB: Waiting for purge to start
2025-09-10 18:36:26 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786498; transaction id 20609
2025-09-10 18:36:26 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-10 18:36:26 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-10 18:36:26 0 [Note] InnoDB: Buffer pool(s) load completed at 250910 18:36:26
2025-09-10 18:36:26 0 [Note] Server socket created on IP: '::'.
2025-09-11 22:06:19 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 22:06:19 0 [Note] InnoDB: Uses event mutexes
2025-09-11 22:06:19 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 22:06:19 0 [Note] InnoDB: Number of pools: 1
2025-09-11 22:06:19 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 22:06:19 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 22:06:19 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 22:06:19 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786507
2025-09-11 22:06:20 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 22:06:20 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-11 22:06:20 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 22:06:20 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 22:06:20 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 22:06:20 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 22:06:20 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786516; transaction id 20609
2025-09-11 22:06:20 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 22:06:20 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 22:06:20 0 [Note] InnoDB: Buffer pool(s) load completed at 250911 22:06:20
2025-09-11 22:06:20 0 [Note] Server socket created on IP: '::'.
2025-09-11 22:54:42 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 22:54:42 0 [Note] InnoDB: Uses event mutexes
2025-09-11 22:54:42 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 22:54:42 0 [Note] InnoDB: Number of pools: 1
2025-09-11 22:54:42 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 22:54:42 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 22:54:42 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 22:54:42 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786525
2025-09-11 22:54:44 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 22:54:44 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-11 22:54:44 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 22:54:44 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 22:54:44 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 22:54:44 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 22:54:44 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786534; transaction id 20609
2025-09-11 22:54:44 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 22:54:44 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 22:54:44 0 [Note] InnoDB: Buffer pool(s) load completed at 250911 22:54:44
2025-09-11 22:54:44 0 [Note] Server socket created on IP: '::'.
2025-09-11 22:54:44 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-09-11 22:54:44 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-09-11 22:54:44 0 [ERROR] Aborting
2025-09-11 22:54:57 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 22:54:57 0 [Note] InnoDB: Uses event mutexes
2025-09-11 22:54:57 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 22:54:57 0 [Note] InnoDB: Number of pools: 1
2025-09-11 22:54:57 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 22:54:57 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 22:54:57 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 22:54:57 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 22:54:57 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 22:54:57 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 22:54:57 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 22:54:57 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 22:54:57 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786543; transaction id 20609
2025-09-11 22:54:57 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 22:54:57 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 22:54:57 0 [Note] Server socket created on IP: '::'.
2025-09-11 22:54:57 0 [ERROR] Can't start server: Bind on TCP/IP port. Got error: 10048: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-09-11 22:54:57 0 [ERROR] Do you already have another mysqld server running on port: 3306 ?
2025-09-11 22:54:57 0 [ERROR] Aborting
2025-09-11 22:57:31 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 22:57:31 0 [Note] InnoDB: Uses event mutexes
2025-09-11 22:57:31 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 22:57:31 0 [Note] InnoDB: Number of pools: 1
2025-09-11 22:57:31 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 22:57:31 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 22:57:31 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 22:57:31 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 22:57:31 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 22:57:31 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 22:57:31 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 22:57:31 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 22:57:31 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786552; transaction id 20609
2025-09-11 22:57:31 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 22:57:31 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 22:57:31 0 [Note] Server socket created on IP: '::'.
2025-09-11 23:02:59 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 23:02:59 0 [Note] InnoDB: Uses event mutexes
2025-09-11 23:02:59 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 23:02:59 0 [Note] InnoDB: Number of pools: 1
2025-09-11 23:02:59 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 23:02:59 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 23:02:59 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 23:02:59 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786577
2025-09-11 23:02:59 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 23:02:59 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-11 23:02:59 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 23:02:59 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 23:02:59 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 23:02:59 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:02:59 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786586; transaction id 20609
2025-09-11 23:02:59 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 23:02:59 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 23:02:59 0 [Note] Server socket created on IP: '0.0.0.0'.
2025-09-11 23:02:59 0 [Note] InnoDB: Buffer pool(s) load completed at 250911 23:02:59
2025-09-11 23:02:59 0 [Note] Reading of all Master_info entries succeeded
2025-09-11 23:02:59 0 [Note] Added new Master_info '' to hash table
2025-09-11 23:02:59 0 [Note] C:\file\mysql\bin\mysqld.exe: ready for connections.
Version: '10.4.22-MariaDB'  socket: ''  port: 3306  mariadb.org binary distribution
2025-09-11 23:03:17 8 [Warning] Aborted connection 8 to db: 'unconnected' user: 'unauthenticated' host: 'localhost' (This connection closed normally without authentication)
2025-09-11 23:03:44 9 [Warning] Aborted connection 9 to db: 'unconnected' user: 'unauthenticated' host: 'localhost' (This connection closed normally without authentication)
2025-09-11 23:04:12 10 [Warning] Aborted connection 10 to db: 'unconnected' user: 'unauthenticated' host: 'localhost' (This connection closed normally without authentication)
2025-09-11 23:04:33 11 [Warning] Aborted connection 11 to db: 'unconnected' user: 'unauthenticated' host: 'localhost' (This connection closed normally without authentication)
2025-09-11 23:04:42 12 [Warning] Aborted connection 12 to db: 'unconnected' user: 'unauthenticated' host: 'localhost' (This connection closed normally without authentication)
2025-09-11 23:54:00 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 23:54:00 0 [Note] InnoDB: Uses event mutexes
2025-09-11 23:54:00 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 23:54:00 0 [Note] InnoDB: Number of pools: 1
2025-09-11 23:54:00 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 23:54:00 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 23:54:00 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 23:54:01 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786595
2025-09-11 23:54:05 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 23:54:06 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-11 23:54:06 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 23:54:06 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 23:54:06 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 23:54:06 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:54:06 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786604; transaction id 20609
2025-09-11 23:54:06 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 23:54:06 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 23:54:06 0 [Note] InnoDB: Buffer pool(s) load completed at 250911 23:54:06
2025-09-11 23:54:06 0 [Note] Server socket created on IP: '::'.
2025-09-11 23:56:07 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-11 23:56:07 0 [Note] InnoDB: Uses event mutexes
2025-09-11 23:56:07 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-11 23:56:07 0 [Note] InnoDB: Number of pools: 1
2025-09-11 23:56:07 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-11 23:56:07 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-11 23:56:07 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-11 23:56:07 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786613
2025-09-11 23:56:07 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-11 23:56:07 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-11 23:56:07 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-11 23:56:07 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-11 23:56:07 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-11 23:56:07 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:56:07 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:56:07 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:56:07 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:56:07 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:56:07 0 [Note] InnoDB: Waiting for purge to start
2025-09-11 23:56:07 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786622; transaction id 20609
2025-09-11 23:56:08 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-11 23:56:08 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-11 23:56:08 0 [Note] Server socket created on IP: '::'.
2025-09-12  0:01:21 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-12  0:01:21 0 [Note] InnoDB: Uses event mutexes
2025-09-12  0:01:21 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-12  0:01:21 0 [Note] InnoDB: Number of pools: 1
2025-09-12  0:01:21 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-12  0:01:21 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-12  0:01:21 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-12  0:01:21 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786631
2025-09-12  0:01:23 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-12  0:01:23 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-12  0:01:23 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-12  0:01:23 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-12  0:01:23 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-12  0:01:23 0 [Note] InnoDB: Waiting for purge to start
2025-09-12  0:01:23 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786640; transaction id 20609
2025-09-12  0:01:23 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-12  0:01:23 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-12  0:01:23 0 [Note] InnoDB: Buffer pool(s) load completed at 250912  0:01:23
2025-09-12  0:01:23 0 [Note] Server socket created on IP: '::'.
2025-09-18 13:58:39 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-18 13:58:39 0 [Note] InnoDB: Uses event mutexes
2025-09-18 13:58:39 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-18 13:58:39 0 [Note] InnoDB: Number of pools: 1
2025-09-18 13:58:39 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-18 13:58:39 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-18 13:58:39 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-18 13:58:39 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786649
2025-09-18 13:58:42 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-18 13:58:42 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-18 13:58:42 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-18 13:58:42 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-18 13:58:42 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-18 13:58:42 0 [Note] InnoDB: Waiting for purge to start
2025-09-18 13:58:42 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786658; transaction id 20609
2025-09-18 13:58:42 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-18 13:58:42 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-18 13:58:42 0 [Note] InnoDB: Buffer pool(s) load completed at 250918 13:58:42
2025-09-18 13:58:42 0 [Note] Server socket created on IP: '::'.
2025-09-18 14:01:28 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-18 14:01:28 0 [Note] InnoDB: Uses event mutexes
2025-09-18 14:01:28 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-18 14:01:28 0 [Note] InnoDB: Number of pools: 1
2025-09-18 14:01:28 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-18 14:01:28 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-18 14:01:28 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-18 14:01:28 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786667
2025-09-18 14:01:28 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-18 14:01:28 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-18 14:01:28 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-18 14:01:28 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-18 14:01:28 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-18 14:01:28 0 [Note] InnoDB: Waiting for purge to start
2025-09-18 14:01:28 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786676; transaction id 20609
2025-09-18 14:01:28 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-18 14:01:28 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-18 14:01:28 0 [Note] Server socket created on IP: '::'.
2025-09-18 14:04:22 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-18 14:04:22 0 [Note] InnoDB: Uses event mutexes
2025-09-18 14:04:22 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-18 14:04:22 0 [Note] InnoDB: Number of pools: 1
2025-09-18 14:04:22 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-18 14:04:22 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-18 14:04:22 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-18 14:04:22 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786685
2025-09-18 14:04:22 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-18 14:04:22 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-18 14:04:22 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-18 14:04:22 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-18 14:04:22 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-18 14:04:22 0 [Note] InnoDB: Waiting for purge to start
2025-09-18 14:04:22 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786694; transaction id 20609
2025-09-18 14:04:22 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-18 14:04:22 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-18 14:04:22 0 [Note] Server socket created on IP: '::'.
2025-09-18 14:13:24 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-18 14:13:24 0 [Note] InnoDB: Uses event mutexes
2025-09-18 14:13:24 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-18 14:13:24 0 [Note] InnoDB: Number of pools: 1
2025-09-18 14:13:24 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-18 14:13:24 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-18 14:13:24 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-18 14:13:24 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786703
2025-09-18 14:13:24 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-18 14:13:24 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-18 14:13:24 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-18 14:13:24 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-18 14:13:24 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-18 14:13:24 0 [Note] InnoDB: Waiting for purge to start
2025-09-18 14:13:24 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786712; transaction id 20609
2025-09-18 14:13:24 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-18 14:13:24 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-18 14:13:24 0 [Note] Server socket created on IP: '::'.
2025-09-18 14:14:01 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-18 14:14:01 0 [Note] InnoDB: Uses event mutexes
2025-09-18 14:14:01 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-18 14:14:01 0 [Note] InnoDB: Number of pools: 1
2025-09-18 14:14:01 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-18 14:14:01 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-18 14:14:01 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-18 14:14:01 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786721
2025-09-18 14:14:02 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-18 14:14:02 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-18 14:14:02 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-18 14:14:02 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-18 14:14:02 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-18 14:14:02 0 [Note] InnoDB: Waiting for purge to start
2025-09-18 14:14:02 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786730; transaction id 20609
2025-09-18 14:14:02 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-18 14:14:02 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-18 14:14:02 0 [Note] Server socket created on IP: '::'.
2025-09-18 14:38:33 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-09-18 14:38:33 0 [Note] InnoDB: Uses event mutexes
2025-09-18 14:38:33 0 [Note] InnoDB: Compressed tables use zlib 1.2.11
2025-09-18 14:38:33 0 [Note] InnoDB: Number of pools: 1
2025-09-18 14:38:33 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-09-18 14:38:33 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-09-18 14:38:33 0 [Note] InnoDB: Completed initialization of buffer pool
2025-09-18 14:38:33 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=14786739
2025-09-18 14:38:33 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-09-18 14:38:33 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-09-18 14:38:33 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-09-18 14:38:33 0 [Note] InnoDB: Setting file 'C:\file\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-09-18 14:38:33 0 [Note] InnoDB: File 'C:\file\mysql\data\ibtmp1' size is now 12 MB.
2025-09-18 14:38:33 0 [Note] InnoDB: Waiting for purge to start
2025-09-18 14:38:34 0 [Note] InnoDB: 10.4.22 started; log sequence number 14786748; transaction id 20609
2025-09-18 14:38:34 0 [Note] InnoDB: Loading buffer pool(s) from C:\file\mysql\data\ib_buffer_pool
2025-09-18 14:38:34 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-09-18 14:38:34 0 [Note] Server socket created on IP: '::'.
