<?php
/**
 * XAMPP MySQL Server Diagnostics Script
 * 
 * This script performs comprehensive diagnostics on XAMPP MySQL configuration
 * to identify potential causes of "MySQL server has gone away" errors.
 */

echo "🔍 XAMPP MySQL Server Diagnostics\n";
echo "==================================\n\n";

// Database connection parameters
$host = '127.0.0.1';
$port = 3306;
$username = 'root';
$password = '';
$database = 'madrasa';

try {
    // Create connection
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 30,
    ]);
    
    echo "✅ Successfully connected to MySQL server\n\n";
    
} catch (PDOException $e) {
    echo "❌ Failed to connect to MySQL server: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n\n";
    
    // Provide troubleshooting suggestions
    echo "🔧 Troubleshooting Suggestions:\n";
    echo "1. Check if XAMPP MySQL service is running\n";
    echo "2. Verify MySQL is listening on port 3306\n";
    echo "3. Check XAMPP control panel for MySQL status\n";
    echo "4. Review MySQL error logs in XAMPP\\mysql\\data\n\n";
    exit(1);
}

// Test 1: Server Information
echo "📊 Test 1: Server Information\n";
echo "-----------------------------\n";

try {
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    echo "MySQL Version: " . $version['version'] . "\n";
    
    $stmt = $pdo->query("SELECT @@hostname as hostname");
    $hostname = $stmt->fetch();
    echo "Hostname: " . $hostname['hostname'] . "\n";
    
    $stmt = $pdo->query("SELECT @@port as port");
    $port_info = $stmt->fetch();
    echo "Port: " . $port_info['port'] . "\n";
    
    $stmt = $pdo->query("SELECT @@datadir as datadir");
    $datadir = $stmt->fetch();
    echo "Data Directory: " . $datadir['datadir'] . "\n";
    
} catch (Exception $e) {
    echo "❌ Error getting server info: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Critical Timeout Variables
echo "⏱️  Test 2: Critical Timeout Variables\n";
echo "-------------------------------------\n";

$critical_variables = [
    'wait_timeout' => 'Time before closing inactive connections',
    'interactive_timeout' => 'Time before closing interactive connections',
    'connect_timeout' => 'Time to wait for connection',
    'net_read_timeout' => 'Time to wait for data from connection',
    'net_write_timeout' => 'Time to wait when writing to connection',
    'slave_net_timeout' => 'Time to wait for slave connection'
];

foreach ($critical_variables as $var => $description) {
    try {
        $stmt = $pdo->query("SHOW VARIABLES LIKE '$var'");
        $result = $stmt->fetch();
        if ($result) {
            $value = $result['Value'];
            $status = '';
            
            // Analyze values
            if ($var === 'wait_timeout' || $var === 'interactive_timeout') {
                if ($value < 3600) {
                    $status = ' ⚠️  (Too low - recommended: 28800+)';
                } elseif ($value >= 28800) {
                    $status = ' ✅ (Good)';
                } else {
                    $status = ' ⚠️  (Consider increasing)';
                }
            } elseif (strpos($var, 'timeout') !== false) {
                if ($value < 60) {
                    $status = ' ⚠️  (Consider increasing)';
                } else {
                    $status = ' ✅ (Good)';
                }
            }
            
            echo sprintf("%-20s: %10s seconds%s\n", $var, number_format($value), $status);
            echo sprintf("%-20s  %s\n", '', $description);
        }
    } catch (Exception $e) {
        echo "❌ Error checking $var: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 3: Memory and Packet Size Variables
echo "💾 Test 3: Memory and Packet Size Variables\n";
echo "------------------------------------------\n";

$memory_variables = [
    'max_allowed_packet' => 'Maximum packet size',
    'max_connections' => 'Maximum concurrent connections',
    'thread_cache_size' => 'Thread cache size',
    'table_open_cache' => 'Table cache size',
    'query_cache_size' => 'Query cache size',
    'innodb_buffer_pool_size' => 'InnoDB buffer pool size',
    'key_buffer_size' => 'MyISAM key buffer size'
];

foreach ($memory_variables as $var => $description) {
    try {
        $stmt = $pdo->query("SHOW VARIABLES LIKE '$var'");
        $result = $stmt->fetch();
        if ($result) {
            $value = $result['Value'];
            $status = '';
            
            // Convert bytes to human readable format for size variables
            if (strpos($var, 'size') !== false || $var === 'max_allowed_packet') {
                $bytes = intval($value);
                if ($bytes >= 1024*1024*1024) {
                    $readable = number_format($bytes / (1024*1024*1024), 2) . ' GB';
                } elseif ($bytes >= 1024*1024) {
                    $readable = number_format($bytes / (1024*1024), 2) . ' MB';
                } elseif ($bytes >= 1024) {
                    $readable = number_format($bytes / 1024, 2) . ' KB';
                } else {
                    $readable = number_format($bytes) . ' bytes';
                }
                
                // Analyze packet size
                if ($var === 'max_allowed_packet' && $bytes < 64*1024*1024) {
                    $status = ' ⚠️  (Consider increasing to 64MB+)';
                } elseif ($var === 'max_allowed_packet') {
                    $status = ' ✅ (Good)';
                }
                
                echo sprintf("%-25s: %15s (%s)%s\n", $var, $readable, number_format($value), $status);
            } else {
                echo sprintf("%-25s: %15s%s\n", $var, number_format($value), $status);
            }
            echo sprintf("%-25s  %s\n", '', $description);
        }
    } catch (Exception $e) {
        echo "❌ Error checking $var: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 4: Connection Status
echo "🔌 Test 4: Connection Status\n";
echo "---------------------------\n";

try {
    $stmt = $pdo->query("SHOW STATUS LIKE 'Connections'");
    $connections = $stmt->fetch();
    echo "Total Connections: " . number_format($connections['Value']) . "\n";
    
    $stmt = $pdo->query("SHOW STATUS LIKE 'Threads_connected'");
    $threads_connected = $stmt->fetch();
    echo "Currently Connected: " . $threads_connected['Value'] . "\n";
    
    $stmt = $pdo->query("SHOW STATUS LIKE 'Threads_running'");
    $threads_running = $stmt->fetch();
    echo "Currently Running: " . $threads_running['Value'] . "\n";
    
    $stmt = $pdo->query("SHOW STATUS LIKE 'Aborted_connects'");
    $aborted_connects = $stmt->fetch();
    echo "Aborted Connections: " . number_format($aborted_connects['Value']) . "\n";
    
    $stmt = $pdo->query("SHOW STATUS LIKE 'Aborted_clients'");
    $aborted_clients = $stmt->fetch();
    echo "Aborted Clients: " . number_format($aborted_clients['Value']) . "\n";
    
    // Calculate connection abort rate
    $abort_rate = ($aborted_connects['Value'] / max($connections['Value'], 1)) * 100;
    if ($abort_rate > 5) {
        echo "⚠️  High connection abort rate: " . number_format($abort_rate, 2) . "%\n";
    } else {
        echo "✅ Connection abort rate: " . number_format($abort_rate, 2) . "%\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking connection status: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Error Log Analysis
echo "📋 Test 5: Error Log Analysis\n";
echo "-----------------------------\n";

try {
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'log_error'");
    $log_error = $stmt->fetch();
    if ($log_error && $log_error['Value']) {
        echo "Error Log Location: " . $log_error['Value'] . "\n";
        
        // Try to read the error log if accessible
        $log_path = $log_error['Value'];
        if (file_exists($log_path) && is_readable($log_path)) {
            echo "✅ Error log is accessible\n";
            
            // Get last few lines of error log
            $lines = file($log_path);
            if ($lines) {
                $recent_lines = array_slice($lines, -10);
                echo "Last 10 lines of error log:\n";
                foreach ($recent_lines as $line) {
                    echo "  " . trim($line) . "\n";
                }
            }
        } else {
            echo "⚠️  Error log not accessible or doesn't exist\n";
            echo "   Check XAMPP\\mysql\\data\\ directory for error logs\n";
        }
    } else {
        echo "⚠️  Error logging may not be enabled\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking error log: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Process List
echo "🔄 Test 6: Current Process List\n";
echo "------------------------------\n";

try {
    $stmt = $pdo->query("SHOW PROCESSLIST");
    $processes = $stmt->fetchAll();
    
    echo "Active Processes: " . count($processes) . "\n";
    echo sprintf("%-5s %-10s %-15s %-10s %-8s %-10s %s\n", 
                 "ID", "User", "Host", "DB", "Command", "Time", "State");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($processes as $process) {
        echo sprintf("%-5s %-10s %-15s %-10s %-8s %-10s %s\n",
                     $process['Id'],
                     substr($process['User'], 0, 10),
                     substr($process['Host'], 0, 15),
                     substr($process['db'] ?? 'NULL', 0, 10),
                     substr($process['Command'], 0, 8),
                     $process['Time'],
                     substr($process['State'] ?? '', 0, 20));
    }
    
} catch (Exception $e) {
    echo "❌ Error getting process list: " . $e->getMessage() . "\n";
}

echo "\n";

// Summary and Recommendations
echo "📝 Summary and Recommendations\n";
echo "=============================\n";

echo "Based on the diagnostics above, here are the key recommendations:\n\n";

echo "1. 🔧 Configuration Issues to Address:\n";
echo "   - Check timeout values (wait_timeout, interactive_timeout)\n";
echo "   - Verify max_allowed_packet size (should be 64MB+)\n";
echo "   - Monitor connection abort rates\n\n";

echo "2. 📁 Files to Check:\n";
echo "   - XAMPP\\mysql\\bin\\my.ini (main config file)\n";
echo "   - XAMPP\\mysql\\data\\*.err (error logs)\n";
echo "   - XAMPP\\apache\\logs\\error.log (Apache errors)\n\n";

echo "3. 🚀 Next Steps:\n";
echo "   - Run the MySQL stability test\n";
echo "   - Check XAMPP control panel for service status\n";
echo "   - Review and update my.ini configuration\n";
echo "   - Restart MySQL service after configuration changes\n\n";

echo "✅ Diagnostics completed successfully!\n";
?>
