<?php
echo "🔧 Applying MySQL Fixes Now\n";
echo "===========================\n\n";

try {
    // Try to connect with longer timeout
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 60,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET SESSION wait_timeout=28800, SESSION interactive_timeout=28800"
    ]);
    
    echo "✅ Connected to MySQL\n\n";
    
    // Apply critical fixes immediately
    echo "Applying timeout fixes...\n";
    
    $fixes = [
        "SET GLOBAL wait_timeout = 28800",
        "SET GLOBAL interactive_timeout = 28800", 
        "SET GLOBAL connect_timeout = 60",
        "SET GLOBAL net_read_timeout = 60",
        "SET GLOBAL net_write_timeout = 60",
        "SET GLOBAL max_allowed_packet = 134217728",
        "SET GLOBAL max_connections = 200"
    ];
    
    foreach ($fixes as $fix) {
        try {
            $pdo->exec($fix);
            echo "✅ Applied: $fix\n";
        } catch (Exception $e) {
            echo "⚠️  Failed: $fix - " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🔍 Verifying fixes...\n";
    
    // Verify the changes
    $checks = [
        'wait_timeout' => 28800,
        'interactive_timeout' => 28800,
        'max_allowed_packet' => 134217728,
        'max_connections' => 200
    ];
    
    foreach ($checks as $variable => $expected) {
        $stmt = $pdo->query("SHOW VARIABLES LIKE '$variable'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $current = intval($result['Value']);
        
        if ($current >= $expected) {
            echo "✅ $variable: " . number_format($current) . "\n";
        } else {
            echo "❌ $variable: " . number_format($current) . " (expected: " . number_format($expected) . ")\n";
        }
    }
    
    echo "\n🧪 Testing connection...\n";
    
    // Test the connection
    $stmt = $pdo->query("SELECT 'Connection working!' as status, NOW() as time");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ " . $result['status'] . " at " . $result['time'] . "\n";
    
    echo "\n🎉 MySQL fixes applied successfully!\n";
    echo "Note: These are temporary fixes. For permanent fixes:\n";
    echo "1. Open XAMPP Control Panel\n";
    echo "2. Stop MySQL service\n";
    echo "3. Start MySQL service\n";
    echo "4. The my.cnf file has been updated with permanent settings\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Manual Fix Instructions:\n";
    echo "1. Open XAMPP Control Panel\n";
    echo "2. Stop MySQL service\n";
    echo "3. Check for error messages\n";
    echo "4. Start MySQL service\n";
    echo "5. If it fails to start, check the error log\n";
    echo "6. The my.cnf file has been updated with correct settings\n";
}
?>
