<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Traits\DatabaseReconnection;

/**
 * Base model with database reconnection capabilities
 * 
 * All models should extend this class to inherit automatic
 * database reconnection and retry logic.
 */
class BaseModel extends Model
{
    use DatabaseReconnection;

    /**
     * Save the model with retry logic
     *
     * @param  array  $options
     * @return bool
     */
    public function save(array $options = [])
    {
        return $this->executeWithRetry(function () use ($options) {
            return parent::save($options);
        });
    }

    /**
     * Delete the model with retry logic
     *
     * @return bool|null
     */
    public function delete()
    {
        return $this->executeWithRetry(function () {
            return parent::delete();
        });
    }

    /**
     * Refresh the model with retry logic
     *
     * @return $this
     */
    public function refresh()
    {
        return $this->executeWithRetry(function () {
            return parent::refresh();
        });
    }

    /**
     * Execute a query with retry logic
     *
     * @param  string  $query
     * @param  array  $bindings
     * @return mixed
     */
    public static function queryWithRetry(string $query, array $bindings = [])
    {
        $instance = new static;
        
        return $instance->executeWithRetry(function () use ($query, $bindings) {
            return \DB::select($query, $bindings);
        });
    }

    /**
     * Execute a raw query with retry logic
     *
     * @param  string  $query
     * @param  array  $bindings
     * @return bool
     */
    public static function statementWithRetry(string $query, array $bindings = [])
    {
        $instance = new static;
        
        return $instance->executeWithRetry(function () use ($query, $bindings) {
            return \DB::statement($query, $bindings);
        });
    }

    /**
     * Begin a database transaction with retry logic
     *
     * @param  callable  $callback
     * @return mixed
     */
    public static function transactionWithRetry(callable $callback)
    {
        $instance = new static;
        
        return $instance->executeWithRetry(function () use ($callback) {
            return \DB::transaction($callback);
        });
    }

    /**
     * Override the newQuery method to add connection health check
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery()
    {
        // Check if database is connected before creating query
        if (!$this->isDatabaseConnected()) {
            $this->reconnectDatabase();
        }
        
        return parent::newQuery();
    }

    /**
     * Handle dynamic method calls with retry logic for common operations
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        // Methods that should use retry logic
        $retryMethods = [
            'update', 'create', 'firstOrCreate', 'updateOrCreate',
            'increment', 'decrement', 'touch'
        ];
        
        if (in_array($method, $retryMethods)) {
            return $this->executeWithRetry(function () use ($method, $parameters) {
                return parent::__call($method, $parameters);
            });
        }
        
        return parent::__call($method, $parameters);
    }

    /**
     * Handle dynamic static method calls with retry logic
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public static function __callStatic($method, $parameters)
    {
        $instance = new static;
        
        // Methods that should use retry logic
        $retryMethods = [
            'create', 'firstOrCreate', 'updateOrCreate', 'insert',
            'insertOrIgnore', 'upsert'
        ];
        
        if (in_array($method, $retryMethods)) {
            return $instance->executeWithRetry(function () use ($method, $parameters) {
                return forward_static_call_array([parent::class, $method], $parameters);
            });
        }
        
        return forward_static_call_array([parent::class, $method], $parameters);
    }
}
