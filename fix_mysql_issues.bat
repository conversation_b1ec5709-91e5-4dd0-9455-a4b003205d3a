@echo off
echo ========================================
echo    MySQL Issue Fix Script
echo ========================================
echo.

echo Step 1: Killing conflicting MySQL process (PID: 28016)
taskkill /F /PID 28016
if %errorlevel% == 0 (
    echo ✅ Successfully killed MySQL process
) else (
    echo ❌ Failed to kill process - you may need to run as Administrator
)
echo.

echo Step 2: Waiting 5 seconds for port to be released...
timeout /t 5 /nobreak > nul
echo.

echo Step 3: Checking if port 3306 is now free...
netstat -ano | findstr :3306
if %errorlevel% == 0 (
    echo ❌ Port 3306 is still in use
) else (
    echo ✅ Port 3306 is now free
)
echo.

echo Step 4: Fixing file permissions for MySQL data directory...
echo Attempting to fix permissions on C:\file\mysql\data\
icacls "C:\file\mysql\data" /grant Everyone:(OI)(CI)F /T
if %errorlevel% == 0 (
    echo ✅ Successfully updated permissions
) else (
    echo ❌ Failed to update permissions - you may need to run as Administrator
)
echo.

echo Step 5: Attempting to start MySQL service...
echo Please manually start MySQL from XAMPP Control Panel now.
echo.

echo ========================================
echo    Fix Complete
echo ========================================
echo.
echo Next steps:
echo 1. Open XAMPP Control Panel as Administrator
echo 2. Start MySQL service
echo 3. Run: php quick_mysql_check.php
echo.
pause
