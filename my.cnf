# MySQL configuration file
[client]
port=3306
socket="C:/file/mysql/mysql.sock"

[mysqld]
port=3306
socket="C:/file/mysql/mysql.sock"
basedir="C:/file/mysql"
tmpdir="C:/file/tmp"
datadir="C:/file/mysql/data"
pid_file="mysql.pid"
key_buffer=16M
max_allowed_packet=128M
sort_buffer_size=512K
net_buffer_length=8K
read_buffer_size=256K
read_rnd_buffer_size=512K
myisam_sort_buffer_size=8M
log_error="mysql_error.log"

# Allow connections from any host
bind-address=0.0.0.0

# Connection settings to prevent "server has gone away"
wait_timeout=28800
interactive_timeout=28800
connect_timeout=60
net_read_timeout=60
net_write_timeout=60

plugin_dir="C:/file/mysql/lib/plugin/"

server-id=1

# InnoDB settings
innodb_data_home_dir="C:/file/mysql/data"
innodb_data_file_path=ibdata1:10M:autoextend
innodb_log_group_home_dir="C:/file/mysql/data"
innodb_buffer_pool_size=16M
innodb_log_file_size=5M
innodb_log_buffer_size=8M
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=120
max_connections=200
thread_cache_size=16

# UTF8 settings
sql_mode=NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION
log_bin_trust_function_creators=1

character-set-server=utf8mb4
collation-server=utf8mb4_general_ci

[mysqldump]
max_allowed_packet=128M

[mysql]
# Remove the next comment character if you are not familiar with SQL
#safe-updates

[isamchk]
key_buffer=20M
sort_buffer_size=20M
read_buffer=2M
write_buffer=2M

[myisamchk]
key_buffer=20M
sort_buffer_size=20M
read_buffer=2M
write_buffer=2M

[mysqlhotcopy]