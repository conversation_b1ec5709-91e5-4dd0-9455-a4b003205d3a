<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PDOException;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Add database reconnection logic
        $this->addDatabaseReconnectionLogic();
        
        // Add query logging for debugging
        if (config('app.debug')) {
            $this->addQueryLogging();
        }
    }

    /**
     * Add database reconnection logic to handle "server has gone away" errors
     */
    private function addDatabaseReconnectionLogic()
    {
        // Listen for database connection events
        DB::listen(function ($query) {
            // This will be called for every query
        });

        // Override the default database manager to add reconnection logic
        $this->app->singleton('db', function ($app) {
            return new \App\Database\DatabaseManager($app, $app['db.factory']);
        });
    }

    /**
     * Add query logging for debugging purposes
     */
    private function addQueryLogging()
    {
        DB::listen(function (QueryExecuted $query) {
            if ($query->time > 1000) { // Log slow queries (> 1 second)
                Log::warning('Slow query detected', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $query->time . 'ms'
                ]);
            }
        });
    }
}
