<?php
/**
 * MySQL Server Stability Test for XAMPP
 * 
 * This script monitors MySQL server stability, connection persistence,
 * and identifies potential crashes or service interruptions.
 */

echo "🔍 MySQL Server Stability Test\n";
echo "==============================\n\n";

// Configuration
$host = '127.0.0.1';
$port = 3306;
$username = 'root';
$password = '';
$database = 'madrasa';
$test_duration = 300; // 5 minutes
$check_interval = 10; // 10 seconds

echo "Test Configuration:\n";
echo "- Duration: {$test_duration} seconds (" . ($test_duration/60) . " minutes)\n";
echo "- Check Interval: {$check_interval} seconds\n";
echo "- Target: {$host}:{$port}\n\n";

// Test results storage
$results = [
    'start_time' => time(),
    'checks' => [],
    'errors' => [],
    'connection_failures' => 0,
    'query_failures' => 0,
    'total_checks' => 0
];

// Function to test connection
function testConnection($host, $port, $username, $password, $database) {
    try {
        $start_time = microtime(true);
        
        $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", 
                       $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 10,
        ]);
        
        $connection_time = (microtime(true) - $start_time) * 1000;
        
        return [
            'success' => true,
            'connection_time' => $connection_time,
            'pdo' => $pdo
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'connection_time' => null,
            'pdo' => null
        ];
    }
}

// Function to test query execution
function testQuery($pdo) {
    try {
        $start_time = microtime(true);
        
        // Test basic query
        $stmt = $pdo->query("SELECT 1 as test, NOW() as current_time, CONNECTION_ID() as connection_id");
        $result = $stmt->fetch();
        
        $query_time = (microtime(true) - $start_time) * 1000;
        
        return [
            'success' => true,
            'query_time' => $query_time,
            'connection_id' => $result['connection_id'],
            'server_time' => $result['current_time']
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'query_time' => null
        ];
    }
}

// Function to get server uptime
function getServerUptime($pdo) {
    try {
        $stmt = $pdo->query("SHOW STATUS LIKE 'Uptime'");
        $result = $stmt->fetch();
        return intval($result['Value']);
    } catch (Exception $e) {
        return null;
    }
}

// Function to check for server restarts
function checkServerRestart($current_uptime, $previous_uptime) {
    if ($previous_uptime !== null && $current_uptime < $previous_uptime) {
        return true;
    }
    return false;
}

echo "🚀 Starting stability test...\n";
echo "Press Ctrl+C to stop the test early.\n\n";

$start_time = time();
$end_time = $start_time + $test_duration;
$check_count = 0;
$previous_uptime = null;
$previous_connection_id = null;

while (time() < $end_time) {
    $check_count++;
    $current_time = date('Y-m-d H:i:s');
    
    echo "[{$current_time}] Check #{$check_count}: ";
    
    // Test connection
    $connection_result = testConnection($host, $port, $username, $password, $database);
    
    if (!$connection_result['success']) {
        echo "❌ CONNECTION FAILED\n";
        echo "  Error: " . $connection_result['error'] . "\n";
        echo "  Code: " . $connection_result['error_code'] . "\n";
        
        $results['connection_failures']++;
        $results['errors'][] = [
            'time' => $current_time,
            'type' => 'connection',
            'error' => $connection_result['error'],
            'code' => $connection_result['error_code']
        ];
        
        sleep($check_interval);
        continue;
    }
    
    $pdo = $connection_result['pdo'];
    
    // Test query execution
    $query_result = testQuery($pdo);
    
    if (!$query_result['success']) {
        echo "❌ QUERY FAILED\n";
        echo "  Error: " . $query_result['error'] . "\n";
        
        $results['query_failures']++;
        $results['errors'][] = [
            'time' => $current_time,
            'type' => 'query',
            'error' => $query_result['error'],
            'code' => $query_result['error_code'] ?? 'N/A'
        ];
        
        sleep($check_interval);
        continue;
    }
    
    // Get server uptime
    $current_uptime = getServerUptime($pdo);
    
    // Check for server restart
    $server_restarted = checkServerRestart($current_uptime, $previous_uptime);
    
    if ($server_restarted) {
        echo "🔄 SERVER RESTART DETECTED\n";
        echo "  Previous uptime: {$previous_uptime}s\n";
        echo "  Current uptime: {$current_uptime}s\n";
        
        $results['errors'][] = [
            'time' => $current_time,
            'type' => 'restart',
            'error' => 'Server restart detected',
            'previous_uptime' => $previous_uptime,
            'current_uptime' => $current_uptime
        ];
    }
    
    // Check for connection ID change (indicates new connection)
    $connection_id_changed = false;
    if ($previous_connection_id !== null && 
        $query_result['connection_id'] != $previous_connection_id) {
        $connection_id_changed = true;
    }
    
    // Display results
    echo "✅ OK";
    echo " (Conn: " . number_format($connection_result['connection_time'], 1) . "ms";
    echo ", Query: " . number_format($query_result['query_time'], 1) . "ms";
    echo ", ID: " . $query_result['connection_id'];
    
    if ($connection_id_changed) {
        echo ", 🔄 NEW CONN";
    }
    
    if ($current_uptime) {
        $uptime_hours = floor($current_uptime / 3600);
        echo ", Uptime: {$uptime_hours}h";
    }
    
    echo ")\n";
    
    // Store results
    $results['checks'][] = [
        'time' => $current_time,
        'connection_time' => $connection_result['connection_time'],
        'query_time' => $query_result['query_time'],
        'connection_id' => $query_result['connection_id'],
        'uptime' => $current_uptime,
        'server_restarted' => $server_restarted,
        'connection_id_changed' => $connection_id_changed
    ];
    
    $previous_uptime = $current_uptime;
    $previous_connection_id = $query_result['connection_id'];
    $results['total_checks']++;
    
    // Close connection
    $pdo = null;
    
    sleep($check_interval);
}

// Calculate statistics
$total_time = time() - $start_time;
$success_rate = (($results['total_checks'] - $results['connection_failures'] - $results['query_failures']) / max($results['total_checks'], 1)) * 100;

// Display final results
echo "\n📊 Test Results Summary\n";
echo "======================\n";
echo "Total Duration: {$total_time} seconds\n";
echo "Total Checks: " . $results['total_checks'] . "\n";
echo "Connection Failures: " . $results['connection_failures'] . "\n";
echo "Query Failures: " . $results['query_failures'] . "\n";
echo "Success Rate: " . number_format($success_rate, 2) . "%\n";

if (!empty($results['checks'])) {
    $connection_times = array_column($results['checks'], 'connection_time');
    $query_times = array_column($results['checks'], 'query_time');
    
    echo "\nPerformance Statistics:\n";
    echo "Average Connection Time: " . number_format(array_sum($connection_times) / count($connection_times), 2) . "ms\n";
    echo "Max Connection Time: " . number_format(max($connection_times), 2) . "ms\n";
    echo "Average Query Time: " . number_format(array_sum($query_times) / count($query_times), 2) . "ms\n";
    echo "Max Query Time: " . number_format(max($query_times), 2) . "ms\n";
}

if (!empty($results['errors'])) {
    echo "\n❌ Errors Encountered:\n";
    foreach ($results['errors'] as $error) {
        echo "[{$error['time']}] {$error['type']}: {$error['error']}\n";
    }
}

// Recommendations
echo "\n💡 Recommendations:\n";
if ($results['connection_failures'] > 0) {
    echo "- High connection failure rate detected\n";
    echo "- Check XAMPP MySQL service status\n";
    echo "- Review MySQL error logs\n";
}

if ($results['query_failures'] > 0) {
    echo "- Query failures detected\n";
    echo "- Check for database corruption\n";
    echo "- Review timeout settings\n";
}

if (count(array_filter($results['checks'], function($check) { return $check['server_restarted']; })) > 0) {
    echo "- Server restarts detected\n";
    echo "- Check system resources (memory, disk space)\n";
    echo "- Review MySQL configuration\n";
}

if ($success_rate < 95) {
    echo "- Low success rate indicates instability\n";
    echo "- Consider increasing timeout values\n";
    echo "- Check system resources\n";
} else {
    echo "✅ Server appears stable\n";
}

echo "\n✅ Stability test completed!\n";
?>
