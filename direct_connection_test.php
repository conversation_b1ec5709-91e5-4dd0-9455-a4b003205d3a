<?php
/**
 * Direct Database Connection Test (Outside Laravel)
 * 
 * This script tests database connections directly using PDO to isolate
 * whether "MySQL server has gone away" errors are server-side or Laravel-specific.
 */

echo "🔍 Direct Database Connection Test\n";
echo "==================================\n\n";

// Configuration
$host = '127.0.0.1';
$port = 3306;
$username = 'root';
$password = '';
$database = 'madrasa';

echo "Test Configuration:\n";
echo "- Host: {$host}\n";
echo "- Port: {$port}\n";
echo "- Database: {$database}\n";
echo "- Username: {$username}\n\n";

// Test 1: Basic Connection Test
echo "📡 Test 1: Basic Connection Test\n";
echo "--------------------------------\n";

try {
    $start_time = microtime(true);
    
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", 
                   $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 30,
        PDO::ATTR_PERSISTENT => false,
    ]);
    
    $connection_time = (microtime(true) - $start_time) * 1000;
    
    echo "✅ Connection successful in " . number_format($connection_time, 2) . "ms\n";
    
    // Get connection info
    $stmt = $pdo->query("SELECT CONNECTION_ID() as id, VERSION() as version");
    $info = $stmt->fetch();
    echo "Connection ID: " . $info['id'] . "\n";
    echo "MySQL Version: " . $info['version'] . "\n\n";
    
} catch (PDOException $e) {
    echo "❌ Connection failed: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n\n";
    exit(1);
}

// Test 2: Long-Running Connection Test
echo "⏱️  Test 2: Long-Running Connection Test\n";
echo "---------------------------------------\n";

echo "Testing connection persistence over time...\n";

$test_duration = 120; // 2 minutes
$check_interval = 15; // 15 seconds
$start_time = time();
$end_time = $start_time + $test_duration;
$check_count = 0;

while (time() < $end_time) {
    $check_count++;
    $elapsed = time() - $start_time;
    
    try {
        $stmt = $pdo->query("SELECT NOW() as current_time, CONNECTION_ID() as conn_id");
        $result = $stmt->fetch();
        
        echo "[{$elapsed}s] Check #{$check_count}: ✅ Connection alive (ID: {$result['conn_id']})\n";
        
    } catch (PDOException $e) {
        echo "[{$elapsed}s] Check #{$check_count}: ❌ Connection failed: " . $e->getMessage() . "\n";
        
        // Try to reconnect
        try {
            $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", 
                           $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 30,
                PDO::ATTR_PERSISTENT => false,
            ]);
            echo "  🔄 Reconnection successful\n";
        } catch (PDOException $reconnect_error) {
            echo "  ❌ Reconnection failed: " . $reconnect_error->getMessage() . "\n";
            break;
        }
    }
    
    sleep($check_interval);
}

echo "\n";

// Test 3: Large Query Test
echo "📊 Test 3: Large Query Test\n";
echo "---------------------------\n";

try {
    // Test with a potentially large result set
    $stmt = $pdo->query("SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH 
                         FROM information_schema.TABLES 
                         WHERE TABLE_SCHEMA = DATABASE()");
    $tables = $stmt->fetchAll();
    
    echo "✅ Large query successful\n";
    echo "Tables found: " . count($tables) . "\n";
    
    if (!empty($tables)) {
        echo "Database tables:\n";
        foreach ($tables as $table) {
            $data_size = $table['DATA_LENGTH'] ? number_format($table['DATA_LENGTH']) . ' bytes' : 'N/A';
            echo "  - {$table['TABLE_NAME']}: {$table['TABLE_ROWS']} rows, {$data_size}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Large query failed: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
}

echo "\n";

// Test 4: Transaction Test
echo "🔄 Test 4: Transaction Test\n";
echo "---------------------------\n";

try {
    // Start transaction
    $pdo->beginTransaction();
    echo "✅ Transaction started\n";
    
    // Test query within transaction
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✅ Query in transaction successful: {$result['count']} users\n";
    
    // Rollback transaction
    $pdo->rollBack();
    echo "✅ Transaction rolled back\n";
    
} catch (PDOException $e) {
    echo "❌ Transaction test failed: " . $e->getMessage() . "\n";
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
        echo "  🔄 Transaction rolled back due to error\n";
    }
}

echo "\n";

// Test 5: Connection Timeout Simulation
echo "⏰ Test 5: Connection Timeout Simulation\n";
echo "---------------------------------------\n";

try {
    // Get current wait_timeout
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'wait_timeout'");
    $timeout_result = $stmt->fetch();
    $wait_timeout = intval($timeout_result['Value']);
    
    echo "Current wait_timeout: {$wait_timeout} seconds\n";
    
    if ($wait_timeout > 60) {
        echo "Simulating idle connection (waiting 30 seconds)...\n";
        
        // Wait for 30 seconds without activity
        sleep(30);
        
        // Try to use connection after idle period
        $stmt = $pdo->query("SELECT 'Connection still alive' as status");
        $result = $stmt->fetch();
        echo "✅ Connection survived idle period: " . $result['status'] . "\n";
        
    } else {
        echo "⚠️  wait_timeout is very low ({$wait_timeout}s) - skipping idle test\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Connection timeout test failed: " . $e->getMessage() . "\n";
    echo "This indicates the connection was lost during idle period\n";
}

echo "\n";

// Test 6: Multiple Connections Test
echo "🔗 Test 6: Multiple Connections Test\n";
echo "------------------------------------\n";

$connections = [];
$max_connections = 5;

try {
    // Create multiple connections
    for ($i = 1; $i <= $max_connections; $i++) {
        $conn = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", 
                        $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 30,
            PDO::ATTR_PERSISTENT => false,
        ]);
        
        $stmt = $conn->query("SELECT CONNECTION_ID() as id");
        $conn_info = $stmt->fetch();
        
        $connections[] = [
            'number' => $i,
            'pdo' => $conn,
            'id' => $conn_info['id']
        ];
        
        echo "Connection #{$i}: ID {$conn_info['id']} ✅\n";
    }
    
    echo "✅ Successfully created {$max_connections} connections\n";
    
    // Test all connections
    echo "Testing all connections...\n";
    foreach ($connections as $conn_data) {
        try {
            $stmt = $conn_data['pdo']->query("SELECT 1 as test");
            $result = $stmt->fetch();
            echo "Connection #{$conn_data['number']} (ID {$conn_data['id']}): ✅ Active\n";
        } catch (PDOException $e) {
            echo "Connection #{$conn_data['number']} (ID {$conn_data['id']}): ❌ Failed\n";
        }
    }
    
    // Close all connections
    foreach ($connections as $conn_data) {
        $conn_data['pdo'] = null;
    }
    echo "✅ All connections closed\n";
    
} catch (PDOException $e) {
    echo "❌ Multiple connections test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 7: Error Simulation
echo "🚨 Test 7: Error Simulation\n";
echo "---------------------------\n";

try {
    // Try to query a non-existent table
    $stmt = $pdo->query("SELECT * FROM non_existent_table_12345");
    echo "❌ This should not appear\n";
    
} catch (PDOException $e) {
    echo "✅ Expected error caught: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
    
    // Verify connection is still usable after error
    try {
        $stmt = $pdo->query("SELECT 'Connection still works' as status");
        $result = $stmt->fetch();
        echo "✅ Connection still usable after error: " . $result['status'] . "\n";
    } catch (PDOException $recovery_error) {
        echo "❌ Connection not usable after error: " . $recovery_error->getMessage() . "\n";
    }
}

echo "\n";

// Final Summary
echo "📋 Test Summary\n";
echo "==============\n";
echo "✅ Basic connection test completed\n";
echo "✅ Long-running connection test completed\n";
echo "✅ Large query test completed\n";
echo "✅ Transaction test completed\n";
echo "✅ Connection timeout simulation completed\n";
echo "✅ Multiple connections test completed\n";
echo "✅ Error simulation completed\n\n";

echo "🎯 Conclusions:\n";
echo "- If all tests passed: The MySQL server is stable\n";
echo "- If tests failed: There are server-side configuration issues\n";
echo "- Compare these results with Laravel application behavior\n";
echo "- Any differences indicate Laravel-specific issues\n\n";

echo "📝 Next Steps:\n";
echo "1. Run the XAMPP MySQL diagnostics script\n";
echo "2. Check MySQL error logs in XAMPP\\mysql\\data\\\n";
echo "3. Review and update MySQL configuration if needed\n";
echo "4. Test your Laravel application after server fixes\n\n";

echo "✅ Direct connection test completed!\n";
?>
