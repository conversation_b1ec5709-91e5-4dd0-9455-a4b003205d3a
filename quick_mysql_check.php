<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '', [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    echo "MySQL Connection: OK\n";
    
    // Check current timeout values
    $stmt = $pdo->query('SHOW VARIABLES LIKE "wait_timeout"');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Current wait_timeout: " . $result['Value'] . " seconds\n";
    
    $stmt = $pdo->query('SHOW VARIABLES LIKE "max_allowed_packet"');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Current max_allowed_packet: " . number_format($result['Value']) . " bytes\n";
    
    $stmt = $pdo->query('SHOW VARIABLES LIKE "interactive_timeout"');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Current interactive_timeout: " . $result['Value'] . " seconds\n";
    
} catch (Exception $e) {
    echo "MySQL Connection Failed: " . $e->getMessage() . "\n";
}
?>
