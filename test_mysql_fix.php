<?php
echo "🧪 Testing MySQL Fix\n";
echo "===================\n\n";

// Test 1: Check if port is free
echo "Test 1: Checking port 3306...\n";
$socket = @fsockopen('127.0.0.1', 3306, $errno, $errstr, 5);
if ($socket) {
    echo "✅ Port 3306 is responding\n";
    fclose($socket);
} else {
    echo "❌ Port 3306 is not responding: $errstr\n";
}
echo "\n";

// Test 2: Try to connect
echo "Test 2: Testing MySQL connection...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 10
    ]);
    
    echo "✅ MySQL connection successful!\n";
    
    // Test 3: Check server variables
    echo "\nTest 3: Checking server variables...\n";
    $stmt = $pdo->query('SHOW VARIABLES LIKE "wait_timeout"');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "wait_timeout: " . $result['Value'] . " seconds\n";
    
    $stmt = $pdo->query('SHOW VARIABLES LIKE "max_allowed_packet"');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "max_allowed_packet: " . number_format($result['Value']) . " bytes\n";
    
    // Test 4: Test a simple query
    echo "\nTest 4: Testing database query...\n";
    $stmt = $pdo->query("SELECT 'MySQL is working!' as status, NOW() as time");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ " . $result['status'] . " at " . $result['time'] . "\n";
    
    echo "\n🎉 ALL TESTS PASSED! MySQL is working correctly.\n";
    
} catch (Exception $e) {
    echo "❌ MySQL connection failed: " . $e->getMessage() . "\n\n";
    
    echo "🔧 TROUBLESHOOTING:\n";
    echo "1. Make sure you ran the commands as Administrator\n";
    echo "2. Check XAMPP Control Panel - MySQL should be green/running\n";
    echo "3. If MySQL won't start, check the error logs\n";
    echo "4. Try restarting your computer if all else fails\n";
}
?>
