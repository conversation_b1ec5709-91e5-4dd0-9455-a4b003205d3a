<?php

namespace App\Database;

use Illuminate\Database\DatabaseManager as BaseDatabaseManager;
use Illuminate\Database\Connection;
use Illuminate\Support\Facades\Log;
use PDOException;

class DatabaseManager extends BaseDatabaseManager
{
    /**
     * Get a database connection instance.
     *
     * @param  string|null  $name
     * @return \Illuminate\Database\Connection
     */
    public function connection($name = null)
    {
        $connection = parent::connection($name);
        
        // Wrap the connection with reconnection logic
        return $this->wrapConnectionWithReconnection($connection);
    }

    /**
     * Wrap a connection with automatic reconnection logic
     *
     * @param  \Illuminate\Database\Connection  $connection
     * @return \Illuminate\Database\Connection
     */
    protected function wrapConnectionWithReconnection(Connection $connection)
    {
        // Override the connection's query methods to add retry logic
        $originalSelect = $connection->select(...);
        
        $connection->macro('selectWithRetry', function ($query, $bindings = [], $useReadPdo = true) use ($connection) {
            return $this->executeWithRetry(function () use ($connection, $query, $bindings, $useReadPdo) {
                return $connection->select($query, $bindings, $useReadPdo);
            }, $connection);
        });

        $connection->macro('statementWithRetry', function ($query, $bindings = []) use ($connection) {
            return $this->executeWithRetry(function () use ($connection, $query, $bindings) {
                return $connection->statement($query, $bindings);
            }, $connection);
        });

        return $connection;
    }

    /**
     * Execute a database operation with retry logic for "server has gone away" errors
     *
     * @param  callable  $callback
     * @param  \Illuminate\Database\Connection  $connection
     * @param  int  $maxRetries
     * @return mixed
     */
    protected function executeWithRetry(callable $callback, Connection $connection, int $maxRetries = 3)
    {
        $attempt = 0;
        
        while ($attempt < $maxRetries) {
            try {
                return $callback();
            } catch (PDOException $e) {
                $attempt++;
                
                if ($this->isConnectionError($e) && $attempt < $maxRetries) {
                    Log::warning("Database connection lost, attempting reconnection (attempt {$attempt}/{$maxRetries})", [
                        'error' => $e->getMessage(),
                        'code' => $e->getCode()
                    ]);
                    
                    // Reconnect to the database
                    $this->reconnectConnection($connection);
                    
                    // Wait before retrying (exponential backoff)
                    sleep(pow(2, $attempt - 1));
                    
                    continue;
                }
                
                // If it's not a connection error or we've exceeded max retries, throw the exception
                throw $e;
            }
        }
    }

    /**
     * Check if the exception is a connection-related error
     *
     * @param  \PDOException  $e
     * @return bool
     */
    protected function isConnectionError(PDOException $e): bool
    {
        $connectionErrors = [
            'server has gone away',
            'no connection to the server',
            'Lost connection',
            'is dead or not enabled',
            'Error while sending',
            'decryption failed or bad record mac',
            'server closed the connection unexpectedly',
            'SSL connection has been closed unexpectedly',
            'Error writing data to the connection',
            'Resource deadlock avoided',
            'Transaction() on null',
            'child connection forced to terminate due to client_idle_limit',
            'query_wait_timeout',
            'reset by peer',
            'Physical connection is not usable',
            'TCP Provider: Error code 0x68',
            'Name or service not known',
            'ORA-03114',
            'Packets out of order. Expected',
            'Adaptive Server connection failed',
            'Communication link failure',
            'connection is no longer usable',
            'Login timeout expired',
            'SQLSTATE[HY000] [2006]',
            'SQLSTATE[HY000] [2013]',
        ];

        $errorMessage = strtolower($e->getMessage());
        
        foreach ($connectionErrors as $error) {
            if (strpos($errorMessage, strtolower($error)) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Reconnect to the database
     *
     * @param  \Illuminate\Database\Connection  $connection
     * @return void
     */
    protected function reconnectConnection(Connection $connection): void
    {
        try {
            $connection->disconnect();
            $connection->reconnect();
            
            Log::info('Database connection successfully restored');
        } catch (\Exception $e) {
            Log::error('Failed to reconnect to database', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
