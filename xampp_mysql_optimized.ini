# Optimized MySQL Configuration for XAMPP
# This configuration is designed to prevent "MySQL server has gone away" errors
# 
# Instructions:
# 1. Backup your current my.ini file (XAMPP\mysql\bin\my.ini)
# 2. Replace the [mysqld] section with the settings below
# 3. Restart MySQL service in XAMPP Control Panel
# 4. Run the validation script to verify changes

[client]
port=3306
socket="C:/file/mysql/mysql.sock"

[mysqld]
# Basic server settings
port=3306
socket="C:/file/mysql/mysql.sock"
basedir="C:/file/mysql"
tmpdir="C:/file/tmp"
datadir="C:/file/mysql/data"
pid_file="mysql.pid"

# Character set and collation
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# SQL Mode (strict but compatible)
sql_mode=STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# === CONNECTION TIMEOUT SETTINGS (Critical for preventing "server has gone away") ===
# Time before closing inactive connections (8 hours)
wait_timeout=28800
# Time before closing interactive connections (8 hours)
interactive_timeout=28800
# Time to wait for connection establishment
connect_timeout=60
# Time to wait for data from connection
net_read_timeout=60
# Time to wait when writing to connection
net_write_timeout=60
# Time to wait for slave connection
slave_net_timeout=60

# === PACKET AND BUFFER SETTINGS ===
# Maximum packet size (128MB - prevents large query failures)
max_allowed_packet=134217728
# Network buffer length
net_buffer_length=32768
# Sort buffer size
sort_buffer_size=2097152
# Read buffer size
read_buffer_size=1048576
# Random read buffer size
read_rnd_buffer_size=2097152

# === CONNECTION SETTINGS ===
# Maximum concurrent connections
max_connections=200
# Maximum connections per user
max_user_connections=100
# Maximum connect errors before blocking host
max_connect_errors=1000
# Thread cache size
thread_cache_size=16
# Back log for connections
back_log=100

# === MEMORY SETTINGS ===
# Key buffer for MyISAM tables
key_buffer_size=32M
# MyISAM sort buffer
myisam_sort_buffer_size=16M
# Table cache
table_open_cache=400
# Table definition cache
table_definition_cache=400

# === QUERY CACHE SETTINGS ===
# Query cache type (1 = ON)
query_cache_type=1
# Query cache size (32MB)
query_cache_size=33554432
# Query cache limit per query
query_cache_limit=2097152

# === INNODB SETTINGS ===
# InnoDB buffer pool size (adjust based on available RAM)
innodb_buffer_pool_size=128M
# InnoDB log file size
innodb_log_file_size=64M
# InnoDB log buffer size
innodb_log_buffer_size=16M
# InnoDB flush method
innodb_flush_method=O_DIRECT
# InnoDB flush log at transaction commit
innodb_flush_log_at_trx_commit=1
# InnoDB lock wait timeout
innodb_lock_wait_timeout=120
# InnoDB file per table
innodb_file_per_table=1
# InnoDB additional memory pool
innodb_additional_mem_pool_size=8M

# === LOGGING SETTINGS ===
# Error log
log_error="mysql_error.log"
# General query log (disable in production for performance)
general_log=0
general_log_file="mysql_general.log"
# Slow query log
slow_query_log=1
slow_query_log_file="mysql_slow.log"
long_query_time=2
log_queries_not_using_indexes=1

# === BINARY LOGGING (for replication) ===
# Enable binary logging
log_bin=mysql-bin
# Binary log format
binlog_format=ROW
# Expire binary logs after 7 days
expire_logs_days=7
# Maximum binary log size
max_binlog_size=100M

# === SECURITY SETTINGS ===
# Bind to all interfaces (change to 127.0.0.1 for localhost only)
bind-address=0.0.0.0
# Skip name resolution for better performance
skip-name-resolve
# Disable LOAD DATA LOCAL INFILE for security
local_infile=0

# === PERFORMANCE SETTINGS ===
# Concurrent insert method
concurrent_insert=2
# Delay key writes for MyISAM
delay_key_write=ALL
# Flush time
flush_time=0
# Join buffer size
join_buffer_size=2097152
# Bulk insert buffer size
bulk_insert_buffer_size=8388608

# === REPLICATION SETTINGS ===
server-id=1
# Relay log
relay_log=mysql-relay-bin
# Log slave updates
log_slave_updates=1

# === TEMPORARY TABLES ===
# Temporary table size
tmp_table_size=64M
# Maximum heap table size
max_heap_table_size=64M

# === FULLTEXT SEARCH ===
# Minimum word length for fulltext search
ft_min_word_len=3
# Maximum word length for fulltext search
ft_max_word_len=84

[mysqldump]
# Maximum packet size for mysqldump
max_allowed_packet=134217728
quick
single_transaction

[mysql]
# Remove the next comment character if you are not familiar with SQL
#safe-updates
default-character-set=utf8mb4

[isamchk]
key_buffer=32M
sort_buffer_size=32M
read_buffer=8M
write_buffer=8M

[myisamchk]
key_buffer=32M
sort_buffer_size=32M
read_buffer=8M
write_buffer=8M

[mysqlhotcopy]
interactive_timeout

# === NOTES ===
# 1. Adjust innodb_buffer_pool_size based on available RAM:
#    - For 4GB RAM: 1G
#    - For 8GB RAM: 2G
#    - For 16GB RAM: 4G
#
# 2. Monitor MySQL performance after changes:
#    - Check error logs regularly
#    - Monitor connection counts
#    - Watch for slow queries
#
# 3. For production environments:
#    - Disable general_log
#    - Set bind-address to specific IP
#    - Adjust max_connections based on load
#
# 4. Test thoroughly after applying changes:
#    - Run the validation script
#    - Test your Laravel application
#    - Monitor for any new issues
#
# 5. Common issues and solutions:
#    - If MySQL won't start: Check error logs
#    - If performance is slow: Increase buffer sizes
#    - If connections are refused: Check max_connections
#    - If queries timeout: Increase timeout values
