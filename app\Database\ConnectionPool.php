<?php

namespace App\Database;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use PDO;
use PDOException;

class ConnectionPool
{
    /**
     * Pool of database connections
     *
     * @var array
     */
    private static $pool = [];

    /**
     * Pool configuration
     *
     * @var array
     */
    private static $config = [
        'min_connections' => 1,
        'max_connections' => 10,
        'connection_timeout' => 30,
        'idle_timeout' => 300,
    ];

    /**
     * Connection usage tracking
     *
     * @var array
     */
    private static $usage = [];

    /**
     * Initialize the connection pool
     *
     * @param array $config
     */
    public static function initialize(array $config = []): void
    {
        self::$config = array_merge(self::$config, $config);
        
        // Create minimum number of connections
        for ($i = 0; $i < self::$config['min_connections']; $i++) {
            self::createConnection();
        }

        Log::info('Database connection pool initialized', [
            'min_connections' => self::$config['min_connections'],
            'max_connections' => self::$config['max_connections']
        ]);
    }

    /**
     * Get a connection from the pool
     *
     * @return PDO|null
     */
    public static function getConnection(): ?PDO
    {
        // Clean up idle connections first
        self::cleanupIdleConnections();

        // Try to get an available connection
        foreach (self::$pool as $id => $connectionData) {
            if (!$connectionData['in_use'] && self::isConnectionAlive($connectionData['connection'])) {
                self::$pool[$id]['in_use'] = true;
                self::$pool[$id]['last_used'] = time();
                self::$usage[$id] = time();
                
                Log::debug('Reusing connection from pool', ['connection_id' => $id]);
                return $connectionData['connection'];
            }
        }

        // If no available connection and we haven't reached max, create new one
        if (count(self::$pool) < self::$config['max_connections']) {
            return self::createConnection();
        }

        // Pool is full, wait for a connection to become available
        $timeout = time() + self::$config['connection_timeout'];
        while (time() < $timeout) {
            foreach (self::$pool as $id => $connectionData) {
                if (!$connectionData['in_use']) {
                    self::$pool[$id]['in_use'] = true;
                    self::$pool[$id]['last_used'] = time();
                    self::$usage[$id] = time();
                    
                    return $connectionData['connection'];
                }
            }
            
            usleep(100000); // Wait 100ms before checking again
        }

        Log::warning('Connection pool timeout - no available connections');
        return null;
    }

    /**
     * Return a connection to the pool
     *
     * @param PDO $connection
     */
    public static function returnConnection(PDO $connection): void
    {
        foreach (self::$pool as $id => $connectionData) {
            if ($connectionData['connection'] === $connection) {
                self::$pool[$id]['in_use'] = false;
                self::$pool[$id]['last_used'] = time();
                unset(self::$usage[$id]);
                
                Log::debug('Connection returned to pool', ['connection_id' => $id]);
                return;
            }
        }
    }

    /**
     * Create a new connection and add it to the pool
     *
     * @return PDO|null
     */
    private static function createConnection(): ?PDO
    {
        try {
            $config = config('database.connections.mysql');
            
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            
            $options = $config['options'] ?? [];
            
            $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
            
            $id = uniqid('conn_');
            self::$pool[$id] = [
                'connection' => $pdo,
                'created_at' => time(),
                'last_used' => time(),
                'in_use' => true
            ];
            
            self::$usage[$id] = time();
            
            Log::debug('New connection created and added to pool', ['connection_id' => $id]);
            
            return $pdo;
            
        } catch (PDOException $e) {
            Log::error('Failed to create database connection for pool', [
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            
            return null;
        }
    }

    /**
     * Check if a connection is still alive
     *
     * @param PDO $connection
     * @return bool
     */
    private static function isConnectionAlive(PDO $connection): bool
    {
        try {
            $connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            Log::debug('Connection is no longer alive', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Clean up idle connections
     */
    private static function cleanupIdleConnections(): void
    {
        $currentTime = time();
        $idleTimeout = self::$config['idle_timeout'];
        
        foreach (self::$pool as $id => $connectionData) {
            if (!$connectionData['in_use'] && 
                ($currentTime - $connectionData['last_used']) > $idleTimeout) {
                
                // Don't remove if we're at minimum connections
                if (count(self::$pool) > self::$config['min_connections']) {
                    unset(self::$pool[$id]);
                    Log::debug('Removed idle connection from pool', ['connection_id' => $id]);
                }
            }
        }
    }

    /**
     * Get pool statistics
     *
     * @return array
     */
    public static function getStats(): array
    {
        $totalConnections = count(self::$pool);
        $activeConnections = count(array_filter(self::$pool, function($conn) {
            return $conn['in_use'];
        }));
        $idleConnections = $totalConnections - $activeConnections;
        
        return [
            'total_connections' => $totalConnections,
            'active_connections' => $activeConnections,
            'idle_connections' => $idleConnections,
            'max_connections' => self::$config['max_connections'],
            'min_connections' => self::$config['min_connections'],
            'pool_utilization' => $totalConnections > 0 ? ($activeConnections / $totalConnections) * 100 : 0
        ];
    }

    /**
     * Close all connections in the pool
     */
    public static function closeAll(): void
    {
        foreach (self::$pool as $id => $connectionData) {
            // PDO connections are closed when the object is destroyed
            unset(self::$pool[$id]);
        }
        
        self::$usage = [];
        
        Log::info('All connections in pool have been closed');
    }

    /**
     * Health check for the connection pool
     *
     * @return array
     */
    public static function healthCheck(): array
    {
        $stats = self::getStats();
        $healthyConnections = 0;
        
        foreach (self::$pool as $connectionData) {
            if (self::isConnectionAlive($connectionData['connection'])) {
                $healthyConnections++;
            }
        }
        
        $health = [
            'status' => $healthyConnections > 0 ? 'healthy' : 'unhealthy',
            'healthy_connections' => $healthyConnections,
            'total_connections' => $stats['total_connections'],
            'stats' => $stats
        ];
        
        if ($health['status'] === 'unhealthy') {
            Log::warning('Connection pool health check failed', $health);
        }
        
        return $health;
    }

    /**
     * Execute a callback with a pooled connection
     *
     * @param callable $callback
     * @return mixed
     */
    public static function execute(callable $callback)
    {
        $connection = self::getConnection();
        
        if (!$connection) {
            throw new \RuntimeException('Unable to get database connection from pool');
        }
        
        try {
            return $callback($connection);
        } finally {
            self::returnConnection($connection);
        }
    }
}
