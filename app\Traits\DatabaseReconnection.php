<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PDOException;

trait DatabaseReconnection
{
    /**
     * Execute a database query with automatic retry on connection failure
     *
     * @param  callable  $callback
     * @param  int  $maxRetries
     * @return mixed
     */
    protected function executeWithRetry(callable $callback, int $maxRetries = 3)
    {
        $attempt = 0;
        
        while ($attempt < $maxRetries) {
            try {
                return $callback();
            } catch (PDOException $e) {
                $attempt++;
                
                if ($this->isConnectionError($e) && $attempt < $maxRetries) {
                    Log::warning("Database connection lost, attempting reconnection (attempt {$attempt}/{$maxRetries})", [
                        'error' => $e->getMessage(),
                        'code' => $e->getCode(),
                        'class' => get_class($this)
                    ]);
                    
                    // Reconnect to the database
                    $this->reconnectDatabase();
                    
                    // Wait before retrying (exponential backoff)
                    sleep(pow(2, $attempt - 1));
                    
                    continue;
                }
                
                // If it's not a connection error or we've exceeded max retries, throw the exception
                throw $e;
            } catch (\Exception $e) {
                // For non-PDO exceptions, check if it might be connection related
                if ($this->isConnectionErrorMessage($e->getMessage()) && $attempt < $maxRetries) {
                    $attempt++;
                    Log::warning("Possible database connection issue, attempting reconnection (attempt {$attempt}/{$maxRetries})", [
                        'error' => $e->getMessage(),
                        'class' => get_class($this)
                    ]);
                    
                    $this->reconnectDatabase();
                    sleep(pow(2, $attempt - 1));
                    continue;
                }
                
                throw $e;
            }
        }
    }

    /**
     * Execute a database transaction with retry logic
     *
     * @param  callable  $callback
     * @param  int  $maxRetries
     * @return mixed
     */
    protected function transactionWithRetry(callable $callback, int $maxRetries = 3)
    {
        return $this->executeWithRetry(function () use ($callback) {
            return DB::transaction($callback);
        }, $maxRetries);
    }

    /**
     * Check if the exception is a connection-related error
     *
     * @param  \PDOException  $e
     * @return bool
     */
    protected function isConnectionError(PDOException $e): bool
    {
        $connectionErrors = [
            2006, // MySQL server has gone away
            2013, // Lost connection to MySQL server during query
            2003, // Can't connect to MySQL server
            1053, // Server shutdown in progress
            1205, // Lock wait timeout exceeded
        ];

        if (in_array($e->getCode(), $connectionErrors)) {
            return true;
        }

        return $this->isConnectionErrorMessage($e->getMessage());
    }

    /**
     * Check if the error message indicates a connection problem
     *
     * @param  string  $message
     * @return bool
     */
    protected function isConnectionErrorMessage(string $message): bool
    {
        $connectionErrorMessages = [
            'server has gone away',
            'no connection to the server',
            'Lost connection',
            'is dead or not enabled',
            'Error while sending',
            'decryption failed or bad record mac',
            'server closed the connection unexpectedly',
            'SSL connection has been closed unexpectedly',
            'Error writing data to the connection',
            'Resource deadlock avoided',
            'Transaction() on null',
            'child connection forced to terminate due to client_idle_limit',
            'query_wait_timeout',
            'reset by peer',
            'Physical connection is not usable',
            'TCP Provider: Error code 0x68',
            'Name or service not known',
            'ORA-03114',
            'Packets out of order. Expected',
            'Adaptive Server connection failed',
            'Communication link failure',
            'connection is no longer usable',
            'Login timeout expired',
            'SQLSTATE[HY000] [2006]',
            'SQLSTATE[HY000] [2013]',
        ];

        $errorMessage = strtolower($message);
        
        foreach ($connectionErrorMessages as $error) {
            if (strpos($errorMessage, strtolower($error)) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Reconnect to the database
     *
     * @return void
     */
    protected function reconnectDatabase(): void
    {
        try {
            DB::purge();
            DB::reconnect();
            
            // Test the connection
            DB::connection()->getPdo();
            
            Log::info('Database connection successfully restored', [
                'class' => get_class($this)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to reconnect to database', [
                'error' => $e->getMessage(),
                'class' => get_class($this)
            ]);
            throw $e;
        }
    }

    /**
     * Check if database connection is alive
     *
     * @return bool
     */
    protected function isDatabaseConnected(): bool
    {
        try {
            DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Ping the database to keep connection alive
     *
     * @return bool
     */
    protected function pingDatabase(): bool
    {
        try {
            DB::select('SELECT 1');
            return true;
        } catch (\Exception $e) {
            Log::warning('Database ping failed', [
                'error' => $e->getMessage(),
                'class' => get_class($this)
            ]);
            return false;
        }
    }
}
