<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\Traits\DatabaseReconnection;
use PDOException;

class DatabaseConnectionCheck
{
    use DatabaseReconnection;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Skip database check for static assets
        if ($this->isAssetRequest($request)) {
            return $next($request);
        }

        // Use retry logic for database connection check
        try {
            $this->executeWithRetry(function () {
                // Test database connection
                DB::connection()->getPdo();

                // Additional check to ensure we can actually query the database
                // This helps catch cases where connection exists but is unusable
                Schema::hasTable('users');
            }, 2); // Only 2 retries for middleware to avoid long delays

        } catch (\Exception $e) {
            Log::error('Database connection failed after retries: ' . $e->getMessage(), [
                'url' => $request->url(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);

            // For API requests, return JSON response
            if ($request->wantsJson() || $request->is('api/*')) {
                return response()->json([
                    'error' => 'Service temporarily unavailable',
                    'message' => 'Database connection failed. Please try again later.',
                    'retry_after' => 30 // Suggest retry after 30 seconds
                ], 503);
            }

            // For web requests, show maintenance page
            return response()->view('errors.maintenance', [
                'error' => config('app.debug') ? $e->getMessage() : 'Database service temporarily unavailable',
                'retry_after' => 30
            ], 503);
        }

        return $next($request);
    }
    
    /**
     * Determine if the request is for a static asset.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    private function isAssetRequest($request)
    {
        $assetExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];
        $path = $request->path();
        
        foreach ($assetExtensions as $extension) {
            if (substr($path, -strlen($extension)) === $extension) {
                return true;
            }
        }
        
        return false;
    }
}