<?php
/**
 * MySQL Server Variables Validator for XAMPP
 * 
 * This script validates MySQL server variables against recommended values
 * to prevent "MySQL server has gone away" errors.
 */

echo "🔍 MySQL Server Variables Validator\n";
echo "===================================\n\n";

// Database connection
$host = '127.0.0.1';
$port = 3306;
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "✅ Connected to MySQL server\n\n";
} catch (PDOException $e) {
    echo "❌ Failed to connect: " . $e->getMessage() . "\n";
    exit(1);
}

// Recommended values for preventing "server has gone away" errors
$recommended_values = [
    'wait_timeout' => [
        'min' => 28800,  // 8 hours
        'recommended' => 28800,
        'description' => 'Time before closing inactive connections',
        'unit' => 'seconds'
    ],
    'interactive_timeout' => [
        'min' => 28800,  // 8 hours
        'recommended' => 28800,
        'description' => 'Time before closing interactive connections',
        'unit' => 'seconds'
    ],
    'max_allowed_packet' => [
        'min' => 67108864,  // 64MB
        'recommended' => 134217728,  // 128MB
        'description' => 'Maximum packet size',
        'unit' => 'bytes'
    ],
    'connect_timeout' => [
        'min' => 10,
        'recommended' => 60,
        'description' => 'Time to wait for connection',
        'unit' => 'seconds'
    ],
    'net_read_timeout' => [
        'min' => 30,
        'recommended' => 60,
        'description' => 'Time to wait for data from connection',
        'unit' => 'seconds'
    ],
    'net_write_timeout' => [
        'min' => 30,
        'recommended' => 60,
        'description' => 'Time to wait when writing to connection',
        'unit' => 'seconds'
    ],
    'max_connections' => [
        'min' => 100,
        'recommended' => 200,
        'description' => 'Maximum concurrent connections',
        'unit' => 'connections'
    ],
    'thread_cache_size' => [
        'min' => 8,
        'recommended' => 16,
        'description' => 'Thread cache size',
        'unit' => 'threads'
    ]
];

// Function to format bytes
function formatBytes($bytes) {
    if ($bytes >= 1024*1024*1024) {
        return number_format($bytes / (1024*1024*1024), 2) . ' GB';
    } elseif ($bytes >= 1024*1024) {
        return number_format($bytes / (1024*1024), 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return number_format($bytes) . ' bytes';
    }
}

// Function to get variable value
function getVariable($pdo, $variable_name) {
    try {
        $stmt = $pdo->query("SHOW VARIABLES LIKE '$variable_name'");
        $result = $stmt->fetch();
        return $result ? $result['Value'] : null;
    } catch (Exception $e) {
        return null;
    }
}

// Function to validate variable
function validateVariable($current_value, $config) {
    $status = 'unknown';
    $message = '';
    
    if ($current_value === null) {
        return ['status' => 'missing', 'message' => 'Variable not found'];
    }
    
    $numeric_value = intval($current_value);
    
    if ($numeric_value >= $config['recommended']) {
        $status = 'excellent';
        $message = 'Optimal value';
    } elseif ($numeric_value >= $config['min']) {
        $status = 'good';
        $message = 'Acceptable, but could be improved';
    } else {
        $status = 'poor';
        $message = 'Below recommended minimum';
    }
    
    return ['status' => $status, 'message' => $message];
}

echo "📊 Variable Validation Results\n";
echo "=============================\n\n";

$validation_results = [];
$issues_found = 0;

foreach ($recommended_values as $variable => $config) {
    $current_value = getVariable($pdo, $variable);
    $validation = validateVariable($current_value, $config);
    
    // Store results
    $validation_results[$variable] = [
        'current' => $current_value,
        'validation' => $validation,
        'config' => $config
    ];
    
    // Display results
    echo sprintf("%-25s: ", $variable);
    
    if ($current_value !== null) {
        $numeric_value = intval($current_value);
        
        // Format display value
        if ($config['unit'] === 'bytes') {
            $display_value = formatBytes($numeric_value) . " ({$current_value})";
        } elseif ($config['unit'] === 'seconds') {
            $hours = floor($numeric_value / 3600);
            $display_value = $hours > 0 ? "{$hours}h ({$current_value}s)" : "{$current_value}s";
        } else {
            $display_value = number_format($numeric_value);
        }
        
        echo $display_value;
        
        // Status indicator
        switch ($validation['status']) {
            case 'excellent':
                echo " ✅";
                break;
            case 'good':
                echo " ⚠️ ";
                break;
            case 'poor':
                echo " ❌";
                $issues_found++;
                break;
            case 'missing':
                echo " ❓";
                $issues_found++;
                break;
        }
        
        echo "\n";
        echo sprintf("%-25s  %s\n", '', $validation['message']);
        echo sprintf("%-25s  Recommended: ", '');
        
        if ($config['unit'] === 'bytes') {
            echo formatBytes($config['recommended']);
        } elseif ($config['unit'] === 'seconds') {
            $rec_hours = floor($config['recommended'] / 3600);
            echo $rec_hours > 0 ? "{$rec_hours}h" : "{$config['recommended']}s";
        } else {
            echo number_format($config['recommended']);
        }
        
        echo "\n";
        echo sprintf("%-25s  %s\n", '', $config['description']);
        
    } else {
        echo "❓ Not found\n";
        $issues_found++;
    }
    
    echo "\n";
}

// Summary
echo "📋 Validation Summary\n";
echo "====================\n";

$total_variables = count($recommended_values);
$excellent_count = 0;
$good_count = 0;
$poor_count = 0;
$missing_count = 0;

foreach ($validation_results as $variable => $result) {
    switch ($result['validation']['status']) {
        case 'excellent':
            $excellent_count++;
            break;
        case 'good':
            $good_count++;
            break;
        case 'poor':
            $poor_count++;
            break;
        case 'missing':
            $missing_count++;
            break;
    }
}

echo "Total Variables Checked: {$total_variables}\n";
echo "✅ Excellent: {$excellent_count}\n";
echo "⚠️  Good: {$good_count}\n";
echo "❌ Poor: {$poor_count}\n";
echo "❓ Missing: {$missing_count}\n\n";

// Recommendations
if ($issues_found > 0) {
    echo "🔧 Configuration Recommendations\n";
    echo "===============================\n\n";
    
    echo "Add or update these settings in your XAMPP MySQL configuration file:\n";
    echo "File location: XAMPP\\mysql\\bin\\my.ini\n\n";
    
    echo "[mysqld]\n";
    
    foreach ($validation_results as $variable => $result) {
        if ($result['validation']['status'] === 'poor' || $result['validation']['status'] === 'missing') {
            $recommended_value = $result['config']['recommended'];
            echo "{$variable} = {$recommended_value}\n";
        }
    }
    
    echo "\nAfter making changes:\n";
    echo "1. Save the my.ini file\n";
    echo "2. Restart MySQL service in XAMPP Control Panel\n";
    echo "3. Run this script again to verify changes\n\n";
    
    // Generate SQL commands for runtime changes (temporary)
    echo "🚀 Temporary Runtime Changes (until restart)\n";
    echo "============================================\n\n";
    
    echo "You can also apply these changes temporarily using SQL commands:\n\n";
    
    foreach ($validation_results as $variable => $result) {
        if ($result['validation']['status'] === 'poor') {
            $recommended_value = $result['config']['recommended'];
            echo "SET GLOBAL {$variable} = {$recommended_value};\n";
        }
    }
    
    echo "\nNote: Runtime changes are temporary and will be lost on MySQL restart.\n";
    echo "Always update the my.ini file for permanent changes.\n\n";
    
} else {
    echo "🎉 All variables are properly configured!\n";
    echo "Your MySQL server settings should help prevent 'server has gone away' errors.\n\n";
}

// Additional checks
echo "🔍 Additional System Checks\n";
echo "===========================\n";

try {
    // Check server uptime
    $stmt = $pdo->query("SHOW STATUS LIKE 'Uptime'");
    $uptime = $stmt->fetch();
    if ($uptime) {
        $uptime_hours = floor($uptime['Value'] / 3600);
        echo "Server Uptime: {$uptime_hours} hours\n";
    }
    
    // Check for recent restarts
    if ($uptime && $uptime['Value'] < 3600) {
        echo "⚠️  Server was recently restarted (less than 1 hour ago)\n";
    }
    
    // Check error log setting
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'log_error'");
    $log_error = $stmt->fetch();
    if ($log_error && $log_error['Value']) {
        echo "Error Log: " . $log_error['Value'] . "\n";
    } else {
        echo "⚠️  Error logging may not be configured\n";
    }
    
    // Check general log setting
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'general_log'");
    $general_log = $stmt->fetch();
    if ($general_log) {
        echo "General Log: " . ($general_log['Value'] === 'ON' ? 'Enabled' : 'Disabled') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during additional checks: " . $e->getMessage() . "\n";
}

echo "\n✅ Variable validation completed!\n";
?>
